# Markdown CMS Configuration
# Copy this file to .env and customize the values

# Flask Configuration
SECRET_KEY=your-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# LLM Configuration
LLM_BASE_URL=http://localhost:11434
LLM_MODEL=llama2
LLM_MAX_TOKENS=2048
LLM_TEMPERATURE=0.7

# Content Configuration
CONTENT_DIR=content
UPLOAD_DIR=static/uploads
MAX_UPLOAD_SIZE=16777216

# Site Configuration
SITE_NAME=Markdown CMS
SITE_DESCRIPTION=A conversational content management system
SITE_URL=http://localhost:5000

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password

# Database Configuration (for conversation storage)
DATABASE_URL=sqlite:///data/cms.db

# Email Configuration (optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Analytics (optional)
GOOGLE_ANALYTICS_ID=
GOOGLE_SEARCH_CONSOLE_ID=

# Social Media (optional)
TWITTER_HANDLE=
FACEBOOK_PAGE=
LINKEDIN_PAGE=

# Performance Settings
CACHE_TIMEOUT=300
ENABLE_COMPRESSION=True
ENABLE_MINIFICATION=False

# Security Settings
ENABLE_CSRF=True
SESSION_TIMEOUT=3600
RATE_LIMIT_PER_MINUTE=60

# Backup Settings
BACKUP_ENABLED=True
BACKUP_INTERVAL=24
BACKUP_RETENTION_DAYS=30
