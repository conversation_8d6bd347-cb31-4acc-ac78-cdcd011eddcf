#!/bin/bash
# <PERSON><PERSON>t to update environment variables in the .env file

# Function to update a value in the .env file
update_env_var() {
    local key=$1
    local value=$2
    local env_file=".env"
    
    # Check if the key exists in the file
    if grep -q "^${key}=" "$env_file"; then
        # Replace the existing value
        sed -i '' "s|^${key}=.*|${key}=${value}|" "$env_file"
    else
        # Add the key-value pair
        echo "${key}=${value}" >> "$env_file"
    fi
}

# Display current settings
echo "Current API Settings:"
echo "--------------------"
grep -E "^(BING_SEARCH_API_KEY|GOOGLE_SEARCH_API_KEY|GOOGLE_SEARCH_CX|SERP_API_KEY)=" .env | sed 's/=.*/=***/'

# Prompt for API keys
read -p "Enter Bing Search API Key (leave empty to keep current): " bing_key
read -p "Enter Google Search API Key (leave empty to keep current): " google_key
read -p "Enter Google Search Engine ID (CX) (leave empty to keep current): " google_cx
read -p "Enter SerpAPI Key (leave empty to keep current): " serp_key

# Update the .env file
if [ ! -z "$bing_key" ]; then
    update_env_var "BING_SEARCH_API_KEY" "$bing_key"
    echo "Updated Bing Search API Key"
fi

if [ ! -z "$google_key" ]; then
    update_env_var "GOOGLE_SEARCH_API_KEY" "$google_key"
    echo "Updated Google Search API Key"
fi

if [ ! -z "$google_cx" ]; then
    update_env_var "GOOGLE_SEARCH_CX" "$google_cx"
    echo "Updated Google Search Engine ID"
fi

if [ ! -z "$serp_key" ]; then
    update_env_var "SERP_API_KEY" "$serp_key"
    echo "Updated SerpAPI Key"
fi

echo "Environment variables updated successfully!"
echo "Restart the application for changes to take effect."