#!/usr/bin/env python3
"""
Setup script for Markdown CMS
Helps users get started quickly with the CMS
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """Print welcome header"""
    print("=" * 60)
    print("🚀 Markdown CMS Setup")
    print("=" * 60)
    print("Welcome to the Markdown-Powered Conversational CMS!")
    print("This script will help you get started quickly.\n")

def check_python_version():
    """Check if Python version is compatible"""
    print("📋 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required.")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible!")
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error installing dependencies.")
        print("   Please run: pip install -r requirements.txt")
        return False

def setup_environment():
    """Set up environment configuration"""
    print("\n⚙️  Setting up environment...")
    
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
            print("   Please edit .env to customize your settings")
        else:
            print("⚠️  .env.example not found, creating basic .env")
            create_basic_env()
    else:
        print("✅ .env file already exists")
    
    return True

def create_basic_env():
    """Create a basic .env file"""
    env_content = """# Markdown CMS Configuration
SECRET_KEY=change-this-secret-key-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# LLM Configuration
LLM_BASE_URL=http://localhost:11434
LLM_MODEL=llama2
LLM_MAX_TOKENS=2048
LLM_TEMPERATURE=0.7

# Content Configuration
CONTENT_DIR=content
UPLOAD_DIR=static/uploads
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        'content/posts',
        'content/pages',
        'static/uploads',
        'data/conversations'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created: {directory}")
    
    return True

def check_ollama():
    """Check if Ollama is available"""
    print("\n🤖 Checking AI service (Ollama)...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running and accessible!")
            models = response.json().get('models', [])
            if models:
                print(f"   Available models: {', '.join([m.get('name', 'unknown') for m in models])}")
            else:
                print("   No models found. You may want to pull a model:")
                print("   ollama pull llama2")
            return True
        else:
            raise Exception("Ollama not responding")
    except Exception as e:
        print("⚠️  Ollama not detected or not running")
        print("   The CMS will work without AI, but for full functionality:")
        print("   1. Install Ollama: https://ollama.ai/")
        print("   2. Pull a model: ollama pull llama2")
        print("   3. Start Ollama service")
        return False

def run_initial_setup():
    """Run the Flask app for initial setup"""
    print("\n🚀 Starting the CMS...")
    print("   The CMS will start at: http://localhost:5000")
    print("   Admin panel: http://localhost:5000/admin")
    print("   Press Ctrl+C to stop the server")
    print("\n" + "=" * 60)
    
    try:
        subprocess.call([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n\n👋 CMS stopped. Thanks for using Markdown CMS!")

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed. Please install dependencies manually.")
        sys.exit(1)
    
    # Set up environment
    if not setup_environment():
        print("\n❌ Environment setup failed.")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("\n❌ Directory creation failed.")
        sys.exit(1)
    
    # Check Ollama
    check_ollama()
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file to customize settings")
    print("2. Start the CMS: python app.py")
    print("3. Visit http://localhost:5000")
    print("4. Go to /admin to create content")
    
    # Ask if user wants to start the CMS now
    start_now = input("\nWould you like to start the CMS now? (y/N): ").lower().strip()
    if start_now in ['y', 'yes']:
        run_initial_setup()
    else:
        print("\nTo start the CMS later, run: python app.py")
        print("Happy content creating! 🚀")

if __name__ == "__main__":
    main()
