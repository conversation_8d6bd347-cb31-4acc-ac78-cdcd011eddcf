"""
Auto Content Generator Service
Generates daily posts based on AI conversation topics
"""

import os
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from collections import Counter
from dataclasses import dataclass

@dataclass
class TopicAnalysis:
    """Analysis of conversation topics"""
    topic: str
    frequency: int
    keywords: List[str]
    last_mentioned: datetime
    sample_messages: List[str]

class AutoContentGenerator:
    """Generates automated content based on AI conversations"""
    
    def __init__(self, content_manager, ai_service, conversation_manager):
        self.content_manager = content_manager
        self.ai_service = ai_service
        self.conversation_manager = conversation_manager
        self.auto_posts_dir = "data/auto_posts"
        os.makedirs(self.auto_posts_dir, exist_ok=True)
        
        # Load configuration
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load auto-generation configuration"""
        default_config = {
            "enabled": True,
            "daily_post_time": "09:00",  # 9 AM
            "min_conversations": 3,  # Minimum conversations needed
            "min_topic_mentions": 2,  # Minimum mentions for a topic
            "post_length": "medium",  # short, medium, long
            "auto_publish": False,  # Auto-publish or save as draft
            "tag_prefix": "ai-generated",
            "excluded_topics": ["test", "hello", "hi", "thanks"]
        }
        
        config_file = os.path.join(self.auto_posts_dir, "config.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    saved_config = json.load(f)
                default_config.update(saved_config)
            except Exception as e:
                print(f"Error loading auto-generation config: {e}")
        
        return default_config
    
    def _save_config(self):
        """Save configuration to file"""
        config_file = os.path.join(self.auto_posts_dir, "config.json")
        try:
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving auto-generation config: {e}")
    
    def _get_last_generation_date(self) -> Optional[datetime]:
        """Get the date of the last auto-generated post"""
        log_file = os.path.join(self.auto_posts_dir, "generation_log.json")
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r') as f:
                    log = json.load(f)
                return datetime.fromisoformat(log.get("last_generation", ""))
            except:
                pass
        return None
    
    def _update_generation_log(self, post_slug: str, topic: str):
        """Update the generation log"""
        log_file = os.path.join(self.auto_posts_dir, "generation_log.json")
        log_data = {
            "last_generation": datetime.now().isoformat(),
            "last_post_slug": post_slug,
            "last_topic": topic,
            "total_generated": 1
        }
        
        # Load existing log to increment counter
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r') as f:
                    existing_log = json.load(f)
                log_data["total_generated"] = existing_log.get("total_generated", 0) + 1
            except:
                pass
        
        try:
            with open(log_file, 'w') as f:
                json.dump(log_data, f, indent=2)
        except Exception as e:
            print(f"Error updating generation log: {e}")
    
    def should_generate_today(self) -> bool:
        """Check if we should generate a post today"""
        if not self.config["enabled"]:
            return False
        
        last_generation = self._get_last_generation_date()
        if last_generation:
            # Check if we already generated today
            today = datetime.now().date()
            last_date = last_generation.date()
            if last_date >= today:
                return False
        
        return True
    
    def analyze_recent_conversations(self, days: int = 1) -> List[TopicAnalysis]:
        """Analyze recent conversations to extract topics"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # Get recent conversations
        conversations = self.conversation_manager.get_recent_conversations(limit=50)
        
        # Collect messages from recent conversations
        all_messages = []
        for conv in conversations:
            if conv.last_message_at >= cutoff_date:
                messages = self.conversation_manager.get_messages(conv.id)
                user_messages = [msg for msg in messages if msg.role == 'user' and msg.timestamp >= cutoff_date]
                all_messages.extend(user_messages)
        
        if len(all_messages) < self.config["min_conversations"]:
            return []
        
        # Extract topics and keywords
        topics = self._extract_topics(all_messages)
        
        # Filter and rank topics
        filtered_topics = []
        for topic_analysis in topics:
            if (topic_analysis.frequency >= self.config["min_topic_mentions"] and 
                topic_analysis.topic.lower() not in self.config["excluded_topics"]):
                filtered_topics.append(topic_analysis)
        
        # Sort by frequency and recency
        filtered_topics.sort(key=lambda x: (x.frequency, x.last_mentioned), reverse=True)
        
        return filtered_topics
    
    def _extract_topics(self, messages) -> List[TopicAnalysis]:
        """Extract topics from conversation messages"""
        # Simple keyword extraction and clustering
        word_freq = Counter()
        topic_messages = {}
        topic_timestamps = {}
        
        for msg in messages:
            # Clean and tokenize message
            words = self._extract_keywords(msg.content)
            
            for word in words:
                word_freq[word] += 1
                
                if word not in topic_messages:
                    topic_messages[word] = []
                    topic_timestamps[word] = []
                
                topic_messages[word].append(msg.content)
                topic_timestamps[word].append(msg.timestamp)
        
        # Create topic analyses
        topics = []
        for word, freq in word_freq.most_common(20):  # Top 20 topics
            if freq >= 2:  # Minimum frequency
                topics.append(TopicAnalysis(
                    topic=word,
                    frequency=freq,
                    keywords=[word],  # Could be expanded to related keywords
                    last_mentioned=max(topic_timestamps[word]),
                    sample_messages=topic_messages[word][:3]  # Sample messages
                ))
        
        return topics
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract meaningful keywords from text"""
        # Simple keyword extraction
        # Remove common words and extract meaningful terms
        
        common_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must',
            'this', 'that', 'these', 'those', 'here', 'there', 'where', 'when', 'why', 'how',
            'what', 'who', 'which', 'whose', 'whom',
            'hello', 'hi', 'hey', 'thanks', 'thank', 'please', 'yes', 'no', 'ok', 'okay'
        }
        
        # Clean text and extract words
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Filter meaningful words
        keywords = []
        for word in words:
            if (len(word) > 3 and 
                word not in common_words and 
                not word.isdigit() and
                word.isalpha()):
                keywords.append(word)
        
        return keywords
    
    def generate_daily_post(self) -> Optional[str]:
        """Generate a daily post based on recent conversations"""
        if not self.should_generate_today():
            return None
        
        # Analyze recent topics
        topics = self.analyze_recent_conversations()
        
        if not topics:
            print("No suitable topics found for auto-generation")
            return None
        
        # Select the most discussed topic
        selected_topic = topics[0]
        
        # Generate post content using AI
        post_content = self._generate_post_content(selected_topic)
        
        if not post_content:
            print("Failed to generate post content")
            return None
        
        # Create the post
        try:
            title = post_content["title"]
            content = post_content["content"]
            tags = post_content["tags"]
            
            # Create post
            post = self.content_manager.create_post(
                title=title,
                content=content,
                tags=tags,
                published=self.config["auto_publish"]
            )
            
            # Update generation log
            self._update_generation_log(post.slug, selected_topic.topic)
            
            print(f"Auto-generated post: {title} (slug: {post.slug})")
            return post.slug
            
        except Exception as e:
            print(f"Error creating auto-generated post: {e}")
            return None
    
    def _generate_post_content(self, topic_analysis: TopicAnalysis) -> Optional[Dict[str, Any]]:
        """Generate post content using AI based on topic analysis"""
        
        # Create a prompt for the AI to generate a blog post
        prompt = f"""Based on recent conversations about "{topic_analysis.topic}", write a comprehensive blog post. 

Topic: {topic_analysis.topic}
Frequency mentioned: {topic_analysis.frequency} times
Sample user questions/comments:
{chr(10).join(f"- {msg[:100]}..." for msg in topic_analysis.sample_messages[:3])}

Please write a blog post that:
1. Addresses the interest in this topic
2. Provides valuable information and insights
3. Is engaging and informative
4. Is approximately 500-800 words
5. Includes practical examples or tips where relevant

Format your response as a JSON object with:
- "title": An engaging blog post title
- "content": The full blog post content in Markdown format
- "tags": An array of 3-5 relevant tags

Make sure the content is original, helpful, and relates to the conversations people have been having about this topic."""
        
        try:
            # Get AI response
            ai_response = self.ai_service.get_response(prompt)
            
            # Try to parse JSON response
            if ai_response.startswith('{') and ai_response.endswith('}'):
                try:
                    return json.loads(ai_response)
                except json.JSONDecodeError:
                    pass
            
            # If not JSON, try to extract content manually
            return self._parse_ai_response(ai_response, topic_analysis.topic)
            
        except Exception as e:
            print(f"Error generating post content with AI: {e}")
            return None
    
    def _parse_ai_response(self, response: str, topic: str) -> Dict[str, Any]:
        """Parse AI response when it's not in JSON format"""
        lines = response.split('\n')
        
        # Try to extract title (usually first line or after "Title:")
        title = f"Exploring {topic.title()}: Insights from Our Community"
        content = response
        
        # Look for title patterns
        for line in lines[:5]:  # Check first 5 lines
            if any(keyword in line.lower() for keyword in ['title:', 'title', '#']):
                potential_title = re.sub(r'^(title:?|#)\s*', '', line, flags=re.IGNORECASE).strip()
                if len(potential_title) > 10:
                    title = potential_title
                    break
        
        # Generate tags based on topic
        tags = [
            self.config["tag_prefix"],
            "community-driven",
            topic.lower(),
            "discussion",
            "insights"
        ]
        
        return {
            "title": title,
            "content": content,
            "tags": tags[:5]  # Limit to 5 tags
        }
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """Get statistics about auto-generation"""
        log_file = os.path.join(self.auto_posts_dir, "generation_log.json")
        
        stats = {
            "enabled": self.config["enabled"],
            "total_generated": 0,
            "last_generation": None,
            "last_topic": None,
            "next_generation": None
        }
        
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r') as f:
                    log = json.load(f)
                stats.update({
                    "total_generated": log.get("total_generated", 0),
                    "last_generation": log.get("last_generation"),
                    "last_topic": log.get("last_topic")
                })
            except:
                pass
        
        # Calculate next generation time
        if self.config["enabled"]:
            now = datetime.now()
            next_gen = now.replace(hour=9, minute=0, second=0, microsecond=0)
            if next_gen <= now:
                next_gen += timedelta(days=1)
            stats["next_generation"] = next_gen.isoformat()
        
        return stats
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update configuration"""
        self.config.update(new_config)
        self._save_config()
    
    def preview_next_post(self) -> Optional[Dict[str, Any]]:
        """Preview what the next auto-generated post would be"""
        topics = self.analyze_recent_conversations()
        
        if not topics:
            return None
        
        selected_topic = topics[0]
        
        return {
            "topic": selected_topic.topic,
            "frequency": selected_topic.frequency,
            "sample_messages": selected_topic.sample_messages,
            "would_generate": len(topics) > 0 and self.config["enabled"]
        }
