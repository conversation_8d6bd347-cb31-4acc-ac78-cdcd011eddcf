"""
Conversation Manager Service
Handles conversation persistence and management
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

@dataclass
class ConversationMessage:
    """Individual conversation message"""
    id: str
    conversation_id: str
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass
class Conversation:
    """Conversation container"""
    id: str
    started_at: datetime
    last_message_at: datetime
    message_count: int
    title: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['started_at'] = self.started_at.isoformat()
        data['last_message_at'] = self.last_message_at.isoformat()
        return data

class ConversationManager:
    """Manages conversation persistence and retrieval"""
    
    def __init__(self, storage_dir: str = "data/conversations"):
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
        # In-memory cache for active conversations
        self._conversation_cache = {}
        self._message_cache = {}
    
    def _get_conversation_file(self, conversation_id: str) -> str:
        """Get file path for conversation metadata"""
        return os.path.join(self.storage_dir, f"{conversation_id}.json")
    
    def _get_messages_file(self, conversation_id: str) -> str:
        """Get file path for conversation messages"""
        return os.path.join(self.storage_dir, f"{conversation_id}_messages.json")
    
    def _save_conversation(self, conversation: Conversation):
        """Save conversation metadata to file"""
        try:
            filepath = self._get_conversation_file(conversation.id)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(conversation.to_dict(), f, indent=2)
            
            # Update cache
            self._conversation_cache[conversation.id] = conversation
            
        except Exception as e:
            print(f"Error saving conversation {conversation.id}: {e}")
    
    def _load_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Load conversation metadata from file"""
        # Check cache first
        if conversation_id in self._conversation_cache:
            return self._conversation_cache[conversation_id]
        
        try:
            filepath = self._get_conversation_file(conversation_id)
            if not os.path.exists(filepath):
                return None
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversation = Conversation(
                id=data['id'],
                started_at=datetime.fromisoformat(data['started_at']),
                last_message_at=datetime.fromisoformat(data['last_message_at']),
                message_count=data['message_count'],
                title=data.get('title')
            )
            
            # Cache it
            self._conversation_cache[conversation_id] = conversation
            return conversation
            
        except Exception as e:
            print(f"Error loading conversation {conversation_id}: {e}")
            return None
    
    def _save_messages(self, conversation_id: str, messages: List[ConversationMessage]):
        """Save messages to file"""
        try:
            filepath = self._get_messages_file(conversation_id)
            data = [msg.to_dict() for msg in messages]
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            
            # Update cache
            self._message_cache[conversation_id] = messages
            
        except Exception as e:
            print(f"Error saving messages for conversation {conversation_id}: {e}")
    
    def _load_messages(self, conversation_id: str) -> List[ConversationMessage]:
        """Load messages from file"""
        # Check cache first
        if conversation_id in self._message_cache:
            return self._message_cache[conversation_id]
        
        try:
            filepath = self._get_messages_file(conversation_id)
            if not os.path.exists(filepath):
                return []
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            messages = []
            for msg_data in data:
                message = ConversationMessage(
                    id=msg_data['id'],
                    conversation_id=msg_data['conversation_id'],
                    role=msg_data['role'],
                    content=msg_data['content'],
                    timestamp=datetime.fromisoformat(msg_data['timestamp'])
                )
                messages.append(message)
            
            # Cache them
            self._message_cache[conversation_id] = messages
            return messages
            
        except Exception as e:
            print(f"Error loading messages for conversation {conversation_id}: {e}")
            return []
    
    def create_conversation(self, title: Optional[str] = None) -> str:
        """Create new conversation and return its ID"""
        conversation_id = str(uuid.uuid4())
        now = datetime.now()
        
        conversation = Conversation(
            id=conversation_id,
            started_at=now,
            last_message_at=now,
            message_count=0,
            title=title
        )
        
        self._save_conversation(conversation)
        
        # Initialize empty messages list
        self._message_cache[conversation_id] = []
        self._save_messages(conversation_id, [])
        
        return conversation_id
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get conversation by ID"""
        return self._load_conversation(conversation_id)
    
    def add_message(self, conversation_id: str, role: str, content: str) -> ConversationMessage:
        """Add message to conversation"""
        # Load conversation
        conversation = self._load_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        # Create message
        message_id = str(uuid.uuid4())
        now = datetime.now()
        
        message = ConversationMessage(
            id=message_id,
            conversation_id=conversation_id,
            role=role,
            content=content,
            timestamp=now
        )
        
        # Load existing messages and add new one
        messages = self._load_messages(conversation_id)
        messages.append(message)
        
        # Update conversation metadata
        conversation.last_message_at = now
        conversation.message_count = len(messages)
        
        # Generate title from first user message if not set
        if not conversation.title and role == 'user' and len(messages) == 1:
            # Use first 50 characters of first message as title
            conversation.title = content[:50] + "..." if len(content) > 50 else content
        
        # Save everything
        self._save_conversation(conversation)
        self._save_messages(conversation_id, messages)
        
        return message
    
    def get_messages(self, conversation_id: str, limit: Optional[int] = None) -> List[ConversationMessage]:
        """Get messages for conversation"""
        messages = self._load_messages(conversation_id)
        
        if limit:
            return messages[-limit:]  # Return last N messages
        
        return messages
    
    def get_recent_conversations(self, limit: int = 10) -> List[Conversation]:
        """Get recent conversations"""
        conversations = []
        
        try:
            # Get all conversation files
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('.json') and not filename.endswith('_messages.json'):
                    conversation_id = filename[:-5]  # Remove .json extension
                    conversation = self._load_conversation(conversation_id)
                    if conversation:
                        conversations.append(conversation)
            
            # Sort by last message time (newest first)
            conversations.sort(key=lambda x: x.last_message_at, reverse=True)
            
            return conversations[:limit]
            
        except Exception as e:
            print(f"Error getting recent conversations: {e}")
            return []
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete conversation and all its messages"""
        try:
            # Remove files
            conv_file = self._get_conversation_file(conversation_id)
            msg_file = self._get_messages_file(conversation_id)
            
            if os.path.exists(conv_file):
                os.remove(conv_file)
            
            if os.path.exists(msg_file):
                os.remove(msg_file)
            
            # Remove from cache
            if conversation_id in self._conversation_cache:
                del self._conversation_cache[conversation_id]
            
            if conversation_id in self._message_cache:
                del self._message_cache[conversation_id]
            
            return True
            
        except Exception as e:
            print(f"Error deleting conversation {conversation_id}: {e}")
            return False
    
    def search_conversations(self, query: str, limit: int = 10) -> List[Conversation]:
        """Search conversations by content"""
        matching_conversations = []
        query_lower = query.lower()
        
        try:
            # Get all conversations
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('.json') and not filename.endswith('_messages.json'):
                    conversation_id = filename[:-5]
                    
                    # Check if title matches
                    conversation = self._load_conversation(conversation_id)
                    if conversation and conversation.title and query_lower in conversation.title.lower():
                        matching_conversations.append(conversation)
                        continue
                    
                    # Check if any message content matches
                    messages = self._load_messages(conversation_id)
                    for message in messages:
                        if query_lower in message.content.lower():
                            if conversation:
                                matching_conversations.append(conversation)
                            break
            
            # Sort by last message time (newest first)
            matching_conversations.sort(key=lambda x: x.last_message_at, reverse=True)
            
            return matching_conversations[:limit]
            
        except Exception as e:
            print(f"Error searching conversations: {e}")
            return []
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get overall conversation statistics"""
        try:
            total_conversations = 0
            total_messages = 0
            
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('.json') and not filename.endswith('_messages.json'):
                    total_conversations += 1
                    conversation_id = filename[:-5]
                    messages = self._load_messages(conversation_id)
                    total_messages += len(messages)
            
            return {
                "total_conversations": total_conversations,
                "total_messages": total_messages,
                "average_messages_per_conversation": total_messages / total_conversations if total_conversations > 0 else 0
            }
            
        except Exception as e:
            print(f"Error getting conversation stats: {e}")
            return {"total_conversations": 0, "total_messages": 0, "average_messages_per_conversation": 0}
