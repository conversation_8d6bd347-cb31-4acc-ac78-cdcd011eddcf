"""
Content Manager Service
Handles Markdown file operations and content management
"""

import os
import re
import yaml
import frontmatter
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, asdict
import markdown

@dataclass
class ContentItem:
    """Base class for content items"""
    title: str
    slug: str
    content: str
    created_at: datetime
    updated_at: datetime
    published: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data

@dataclass
class Post(ContentItem):
    """Blog post with additional fields"""
    tags: List[str] = None
    excerpt: str = ""
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class Page(ContentItem):
    """Static page"""
    menu_order: int = 0

class ContentManager:
    """Manages Markdown content files"""
    
    def __init__(self, content_dir: str = "content"):
        self.content_dir = content_dir
        self.posts_dir = os.path.join(content_dir, "posts")
        self.pages_dir = os.path.join(content_dir, "pages")
        
        # Ensure directories exist
        os.makedirs(self.posts_dir, exist_ok=True)
        os.makedirs(self.pages_dir, exist_ok=True)
        
        # Initialize markdown processor
        self.md = markdown.Markdown(extensions=[
            'meta',
            'codehilite',
            'fenced_code',
            'tables',
            'toc'
        ])
    
    def _slugify(self, title: str) -> str:
        """Convert title to URL-friendly slug"""
        slug = re.sub(r'[^\w\s-]', '', title.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    def _read_markdown_file(self, filepath: str) -> Optional[Dict[str, Any]]:
        """Read and parse markdown file with frontmatter"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                post = frontmatter.load(f)
                
            # Get file stats
            stat = os.stat(filepath)
            created_at = datetime.fromtimestamp(stat.st_ctime)
            updated_at = datetime.fromtimestamp(stat.st_mtime)
            
            return {
                'content': post.content,
                'metadata': post.metadata,
                'created_at': created_at,
                'updated_at': updated_at
            }
        except Exception as e:
            print(f"Error reading file {filepath}: {e}")
            return None
    
    def _write_markdown_file(self, filepath: str, content: str, metadata: Dict[str, Any]):
        """Write markdown file with frontmatter"""
        try:
            post = frontmatter.Post(content, **metadata)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(frontmatter.dumps(post))
            return True
        except Exception as e:
            print(f"Error writing file {filepath}: {e}")
            return False
    
    def _generate_excerpt(self, content: str, length: int = 150) -> str:
        """Generate excerpt from content"""
        # Remove markdown formatting for excerpt
        plain_text = re.sub(r'[#*`\[\]()]', '', content)
        plain_text = re.sub(r'\n+', ' ', plain_text)
        
        if len(plain_text) <= length:
            return plain_text
        
        # Find last complete word within length
        excerpt = plain_text[:length]
        last_space = excerpt.rfind(' ')
        if last_space > 0:
            excerpt = excerpt[:last_space]
        
        return excerpt + "..."
    
    # Post methods
    def get_all_posts(self, limit: Optional[int] = None) -> List[Post]:
        """Get all blog posts"""
        posts = []
        
        if not os.path.exists(self.posts_dir):
            return posts
        
        for filename in os.listdir(self.posts_dir):
            if filename.endswith('.md'):
                filepath = os.path.join(self.posts_dir, filename)
                data = self._read_markdown_file(filepath)
                
                if data:
                    slug = os.path.splitext(filename)[0]
                    post = Post(
                        title=data['metadata'].get('title', slug.replace('-', ' ').title()),
                        slug=slug,
                        content=data['content'],
                        created_at=data['created_at'],
                        updated_at=data['updated_at'],
                        published=data['metadata'].get('published', False),
                        tags=data['metadata'].get('tags', []),
                        excerpt=data['metadata'].get('excerpt', self._generate_excerpt(data['content']))
                    )
                    posts.append(post)
        
        # Sort by creation date (newest first)
        posts.sort(key=lambda x: x.created_at, reverse=True)
        
        if limit:
            posts = posts[:limit]
        
        return posts
    
    def get_post_by_slug(self, slug: str) -> Optional[Post]:
        """Get specific post by slug"""
        filepath = os.path.join(self.posts_dir, f"{slug}.md")
        
        if not os.path.exists(filepath):
            return None
        
        data = self._read_markdown_file(filepath)
        if not data:
            return None
        
        return Post(
            title=data['metadata'].get('title', slug.replace('-', ' ').title()),
            slug=slug,
            content=data['content'],
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            published=data['metadata'].get('published', False),
            tags=data['metadata'].get('tags', []),
            excerpt=data['metadata'].get('excerpt', self._generate_excerpt(data['content']))
        )
    
    def create_post(self, title: str, content: str, slug: Optional[str] = None, 
                   tags: List[str] = None, published: bool = False) -> Post:
        """Create new blog post"""
        if not slug:
            slug = self._slugify(title)
        
        if tags is None:
            tags = []
        
        # Ensure unique slug
        original_slug = slug
        counter = 1
        while os.path.exists(os.path.join(self.posts_dir, f"{slug}.md")):
            slug = f"{original_slug}-{counter}"
            counter += 1
        
        metadata = {
            'title': title,
            'published': published,
            'tags': tags,
            'excerpt': self._generate_excerpt(content)
        }
        
        filepath = os.path.join(self.posts_dir, f"{slug}.md")
        
        if self._write_markdown_file(filepath, content, metadata):
            now = datetime.now()
            return Post(
                title=title,
                slug=slug,
                content=content,
                created_at=now,
                updated_at=now,
                published=published,
                tags=tags,
                excerpt=metadata['excerpt']
            )
        else:
            raise Exception("Failed to create post")
    
    def update_post(self, slug: str, title: Optional[str] = None, 
                   content: Optional[str] = None, tags: Optional[List[str]] = None,
                   published: Optional[bool] = None) -> Optional[Post]:
        """Update existing post"""
        post = self.get_post_by_slug(slug)
        if not post:
            return None
        
        # Update fields if provided
        if title is not None:
            post.title = title
        if content is not None:
            post.content = content
            post.excerpt = self._generate_excerpt(content)
        if tags is not None:
            post.tags = tags
        if published is not None:
            post.published = published
        
        post.updated_at = datetime.now()
        
        metadata = {
            'title': post.title,
            'published': post.published,
            'tags': post.tags,
            'excerpt': post.excerpt
        }
        
        filepath = os.path.join(self.posts_dir, f"{slug}.md")
        
        if self._write_markdown_file(filepath, post.content, metadata):
            return post
        else:
            return None
    
    def delete_post(self, slug: str) -> bool:
        """Delete post"""
        filepath = os.path.join(self.posts_dir, f"{slug}.md")
        
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                return True
            except Exception as e:
                print(f"Error deleting post {slug}: {e}")
                return False
        
        return False

    # Page methods
    def get_all_pages(self) -> List[Page]:
        """Get all pages"""
        pages = []

        if not os.path.exists(self.pages_dir):
            return pages

        for filename in os.listdir(self.pages_dir):
            if filename.endswith('.md'):
                filepath = os.path.join(self.pages_dir, filename)
                data = self._read_markdown_file(filepath)

                if data:
                    slug = os.path.splitext(filename)[0]
                    page = Page(
                        title=data['metadata'].get('title', slug.replace('-', ' ').title()),
                        slug=slug,
                        content=data['content'],
                        created_at=data['created_at'],
                        updated_at=data['updated_at'],
                        published=data['metadata'].get('published', False),
                        menu_order=data['metadata'].get('menu_order', 0)
                    )
                    pages.append(page)

        # Sort by menu order, then by title
        pages.sort(key=lambda x: (x.menu_order, x.title))
        return pages

    def get_page_by_slug(self, slug: str) -> Optional[Page]:
        """Get specific page by slug"""
        filepath = os.path.join(self.pages_dir, f"{slug}.md")

        if not os.path.exists(filepath):
            return None

        data = self._read_markdown_file(filepath)
        if not data:
            return None

        return Page(
            title=data['metadata'].get('title', slug.replace('-', ' ').title()),
            slug=slug,
            content=data['content'],
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            published=data['metadata'].get('published', False),
            menu_order=data['metadata'].get('menu_order', 0)
        )

    def create_page(self, title: str, content: str, slug: Optional[str] = None,
                   published: bool = False, menu_order: int = 0) -> Page:
        """Create new page"""
        if not slug:
            slug = self._slugify(title)

        # Ensure unique slug
        original_slug = slug
        counter = 1
        while os.path.exists(os.path.join(self.pages_dir, f"{slug}.md")):
            slug = f"{original_slug}-{counter}"
            counter += 1

        metadata = {
            'title': title,
            'published': published,
            'menu_order': menu_order
        }

        filepath = os.path.join(self.pages_dir, f"{slug}.md")

        if self._write_markdown_file(filepath, content, metadata):
            now = datetime.now()
            return Page(
                title=title,
                slug=slug,
                content=content,
                created_at=now,
                updated_at=now,
                published=published,
                menu_order=menu_order
            )
        else:
            raise Exception("Failed to create page")

    def update_page(self, slug: str, title: Optional[str] = None,
                   content: Optional[str] = None, published: Optional[bool] = None,
                   menu_order: Optional[int] = None) -> Optional[Page]:
        """Update existing page"""
        page = self.get_page_by_slug(slug)
        if not page:
            return None

        # Update fields if provided
        if title is not None:
            page.title = title
        if content is not None:
            page.content = content
        if published is not None:
            page.published = published
        if menu_order is not None:
            page.menu_order = menu_order

        page.updated_at = datetime.now()

        metadata = {
            'title': page.title,
            'published': page.published,
            'menu_order': page.menu_order
        }

        filepath = os.path.join(self.pages_dir, f"{slug}.md")

        if self._write_markdown_file(filepath, page.content, metadata):
            return page
        else:
            return None

    def delete_page(self, slug: str) -> bool:
        """Delete page"""
        filepath = os.path.join(self.pages_dir, f"{slug}.md")

        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                return True
            except Exception as e:
                print(f"Error deleting page {slug}: {e}")
                return False

        return False

    def render_markdown(self, content: str) -> str:
        """Render markdown content to HTML"""
        return self.md.convert(content)

    def search_content(self, query: str, content_type: str = 'all') -> List[ContentItem]:
        """Search through content"""
        results = []
        query_lower = query.lower()

        if content_type in ['all', 'posts']:
            posts = self.get_all_posts()
            for post in posts:
                if (query_lower in post.title.lower() or
                    query_lower in post.content.lower() or
                    any(query_lower in tag.lower() for tag in post.tags)):
                    results.append(post)

        if content_type in ['all', 'pages']:
            pages = self.get_all_pages()
            for page in pages:
                if (query_lower in page.title.lower() or
                    query_lower in page.content.lower()):
                    results.append(page)

        return results
