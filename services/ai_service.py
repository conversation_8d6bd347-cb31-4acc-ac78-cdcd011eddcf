"""
AI Service
Handles communication with local LLM for conversational features
"""

import os
import json
import re
import logging
import requests
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Import the web search service
from services.web_search_service import WebSearchService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIService:
    """Service for interacting with local LLM"""

    def __init__(self, content_manager=None):
        # Configuration for local LLM (LM Studio by default)
        self.base_url = os.environ.get('LLM_BASE_URL', 'http://localhost:1234')
        self.model_name = os.environ.get('LLM_MODEL', 'microsoft/phi-4-mini-reasoning')
        self.max_tokens = int(os.environ.get('LLM_MAX_TOKENS', '2048'))
        self.temperature = float(os.environ.get('LLM_TEMPERATURE', '0.7'))

        # Content manager for creating posts
        self.content_manager = content_manager
        
        # Initialize web search service
        self.web_search = WebSearchService()
        
        # Web search configuration
        self.enable_web_search = os.environ.get('ENABLE_WEB_SEARCH', 'true').lower() == 'true'
        self.search_threshold = float(os.environ.get('SEARCH_CONFIDENCE_THRESHOLD', '0.6'))
        self.search_log_dir = os.environ.get('SEARCH_LOG_DIR', 'data/search_logs')
        
        # Create search log directory
        os.makedirs(self.search_log_dir, exist_ok=True)
        
        # Track searches to avoid redundant queries
        self.recent_searches = {}
        
        # System prompt for the talk show host persona
        self.system_prompt = """You are a friendly website assistant with access to web search capabilities. You help visitors and can create content.

IMPORTANT CAPABILITIES:
- I can search the internet for current information when needed
- I can create and edit posts using special formats
- I can answer questions about any topic using web search if I don't know the answer

WEB SEARCH ABILITIES:
When users ask about current events, facts, or information I don't know, I automatically search the web to provide accurate, up-to-date information. I will clearly indicate when my response is based on web search results.

CONTENT CREATION (for NEW posts):
When users say "create a post" or "write an article", use this EXACT format:

[CREATE_POST]
Title: Your Post Title Here
Tags: tag1, tag2, tag3
Published: false
Content:
# Your Post Title Here

Write your content here in markdown format.
[/CREATE_POST]

EDITING EXISTING CONTENT:
When users say "edit the post [name]", "update the [title] post", "modify content", use this EXACT format:

[EDIT_POST]
Slug: existing-post-slug
Content:
# Updated content here

Your updated markdown content...
[/EDIT_POST]

LISTING POSTS:
When users ask "list posts", use: [LIST_POSTS][/LIST_POSTS]

CRITICAL RULES:
- EDIT = use EDIT_POST format (for existing posts)
- CREATE = use CREATE_POST format (for new posts)
- Always include the slug when editing
- If I don't know something, I will search the web for current information
- Be helpful and friendly"""
        
        # Conversation context storage
        self.conversations = {}
    
    def _make_request(self, prompt: str, conversation_id: Optional[str] = None) -> str:
        """Make request to local LLM"""
        try:
            # Get conversation context
            context = self._get_conversation_context(conversation_id) if conversation_id else []
            
            # Prepare messages for the LLM
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history
            for msg in context[-10:]:  # Keep last 10 messages for context
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Add current user message
            messages.append({"role": "user", "content": prompt})
            
            # Try LM Studio API format first
            response = self._try_lmstudio_api(messages)
            if response:
                return response

            # Fallback to simple completion API
            return self._try_completion_api(prompt, context)
            
        except Exception as e:
            print(f"Error communicating with LLM: {e}")
            return self._get_fallback_response(prompt)
    
    def _try_lmstudio_api(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """Try LM Studio OpenAI-compatible API format"""
        try:
            url = f"{self.base_url}/v1/chat/completions"
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": False
            }

            response = requests.post(url, json=payload, timeout=60)

            if response.status_code == 200:
                data = response.json()
                content = data.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
                # Filter out thinking process and return only the actual response
                return self._clean_response(content)

        except Exception as e:
            print(f"LM Studio API error: {e}")

        return None

    def _clean_response(self, content: str) -> str:
        """Clean the AI response by removing thinking process and formatting"""
        import re

        # Remove <think>...</think> blocks
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

        # Remove any remaining XML-like tags
        content = re.sub(r'<[^>]+>', '', content)

        # Clean up extra whitespace and newlines
        content = re.sub(r'\n\s*\n', '\n\n', content)  # Multiple newlines to double
        content = content.strip()

        # If the response is empty after cleaning, provide a fallback
        if not content:
            content = "I'm here to help! What would you like to know about this site?"

        return content

    def _process_content_creation(self, response: str) -> str:
        """Process content creation commands in AI response"""
        if not self.content_manager:
            return response

        import re

        print(f"DEBUG: Processing AI response for content creation: {response[:200]}...")

        # Check for CREATE_POST command (make closing tag optional)
        post_match = re.search(r'\[CREATE_POST\](.*?)(?:\[/CREATE_POST\]|$)', response, re.DOTALL)
        if post_match:
            print("DEBUG: Found CREATE_POST command")
            try:
                post_content = post_match.group(1).strip()
                post_data = self._parse_content_creation(post_content)

                # Create the post
                post = self.content_manager.create_post(
                    title=post_data['title'],
                    content=post_data['content'],
                    tags=post_data.get('tags', []),
                    published=post_data.get('published', False)
                )

                # Replace the command with success message
                success_msg = f"✅ I've created the post '{post.title}' for you! You can view it at /post/{post.slug} or edit it in the admin panel."
                response = response.replace(post_match.group(0), success_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't create the post. Error: {str(e)}"
                response = response.replace(post_match.group(0), error_msg)

        # Check for CREATE_PAGE command (make closing tag optional)
        page_match = re.search(r'\[CREATE_PAGE\](.*?)(?:\[/CREATE_PAGE\]|$)', response, re.DOTALL)
        if page_match:
            try:
                page_content = page_match.group(1).strip()
                page_data = self._parse_content_creation(page_content)

                # Create the page
                page = self.content_manager.create_page(
                    title=page_data['title'],
                    content=page_data['content'],
                    published=page_data.get('published', False)
                )

                # Replace the command with success message
                success_msg = f"✅ I've created the page '{page.title}' for you! You can view it at /page/{page.slug} or edit it in the admin panel."
                response = response.replace(page_match.group(0), success_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't create the page. Error: {str(e)}"
                response = response.replace(page_match.group(0), error_msg)

        # Check for EDIT_POST command (more flexible matching)
        edit_match = re.search(r'\[EDIT_POST\]\s*(.*?)(?:\[/EDIT_POST\]|\[CREATE_POST\]|\[LIST_POSTS\]|$)', response, re.DOTALL)
        if edit_match:
            print("DEBUG: Found EDIT_POST command")
            try:
                edit_content = edit_match.group(1).strip()
                edit_data = self._parse_edit_command(edit_content)

                # Get the existing post
                post = self.content_manager.get_post_by_slug(edit_data['slug'])
                if not post:
                    error_msg = f"❌ Sorry, I couldn't find a post with slug '{edit_data['slug']}'. Please check the post name."
                    response = response.replace(edit_match.group(0), error_msg)
                else:
                    # Update the post
                    updated_post = self.content_manager.update_post(
                        slug=edit_data['slug'],
                        title=edit_data.get('title', post.title),
                        content=edit_data.get('content', post.content),
                        tags=edit_data.get('tags', post.tags),
                        published=edit_data.get('published', post.published)
                    )

                    # Replace the command with success message
                    success_msg = f"✅ I've updated the post '{updated_post.title}'! You can view it at /post/{updated_post.slug} or edit it further in the admin panel."
                    response = response.replace(edit_match.group(0), success_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't edit the post. Error: {str(e)}"
                response = response.replace(edit_match.group(0), error_msg)

        # Check for LIST_POSTS command (make closing tag optional)
        list_match = re.search(r'\[LIST_POSTS\](.*?)(?:\[/LIST_POSTS\]|$)', response, re.DOTALL)
        if list_match:
            print("DEBUG: Found LIST_POSTS command")
            try:
                posts = self.content_manager.get_all_posts()

                if not posts:
                    list_msg = "📝 No posts found. You can create your first post by asking me to 'create a post about [topic]'."
                else:
                    list_msg = "📝 **Available Posts:**\n\n"
                    for post in posts[:10]:  # Limit to 10 posts
                        status = "✅ Published" if post.published else "📝 Draft"
                        list_msg += f"• **{post.title}** (slug: `{post.slug}`) - {status}\n"

                    if len(posts) > 10:
                        list_msg += f"\n...and {len(posts) - 10} more posts. You can edit any post by saying 'edit the post [slug]' or 'update the [title] post'."
                    else:
                        list_msg += "\nYou can edit any post by saying 'edit the post [slug]' or 'update the [title] post'."

                response = response.replace(list_match.group(0), list_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't list the posts. Error: {str(e)}"
                response = response.replace(list_match.group(0), error_msg)

        return response

    def _parse_content_creation(self, content: str) -> Dict[str, Any]:
        """Parse content creation command"""
        lines = content.split('\n')
        data = {
            'title': 'Untitled',
            'content': '',
            'tags': [],
            'published': False
        }

        content_started = False
        content_lines = []

        for line in lines:
            line = line.strip()

            if line.startswith('Title:'):
                data['title'] = line.replace('Title:', '').strip()
            elif line.startswith('Tags:'):
                tags_str = line.replace('Tags:', '').strip()
                if tags_str:
                    # Parse tags - handle both comma and space separated
                    tags = [tag.strip().strip('[]') for tag in tags_str.replace(',', ' ').split()]
                    data['tags'] = [tag for tag in tags if tag]
            elif line.startswith('Published:'):
                pub_str = line.replace('Published:', '').strip().lower()
                data['published'] = pub_str in ['true', 'yes', '1']
            elif line.startswith('Content:'):
                content_started = True
            elif content_started:
                content_lines.append(line)

        # Join content lines
        if content_lines:
            data['content'] = '\n'.join(content_lines).strip()

        return data

    def _parse_edit_command(self, content: str) -> Dict[str, Any]:
        """Parse edit command content"""
        lines = content.split('\n')
        data = {
            'slug': None,
            'title': None,
            'content': None,
            'tags': None,
            'published': None
        }

        content_started = False
        content_lines = []

        for line in lines:
            line = line.strip()

            if line.startswith('Slug:'):
                data['slug'] = line.replace('Slug:', '').strip()
            elif line.startswith('Title:'):
                data['title'] = line.replace('Title:', '').strip()
            elif line.startswith('Tags:'):
                tags_str = line.replace('Tags:', '').strip()
                if tags_str:
                    # Parse tags - handle both comma and space separated
                    tags = [tag.strip().strip('[]') for tag in tags_str.replace(',', ' ').split()]
                    data['tags'] = [tag for tag in tags if tag]
            elif line.startswith('Published:'):
                pub_str = line.replace('Published:', '').strip().lower()
                data['published'] = pub_str in ['true', 'yes', '1']
            elif line.startswith('Content:'):
                content_started = True
            elif content_started:
                content_lines.append(line)

        # Join content lines
        if content_lines:
            data['content'] = '\n'.join(content_lines).strip()

        # Remove None values to only update specified fields
        data = {k: v for k, v in data.items() if v is not None}

        return data

    def _try_completion_api(self, prompt: str, context: List[Dict[str, str]]) -> str:
        """Try simple completion API as fallback"""
        try:
            # Build context string
            context_str = ""
            for msg in context[-5:]:  # Last 5 messages
                role = "Human" if msg["role"] == "user" else "Assistant"
                context_str += f"{role}: {msg['content']}\n"
            
            full_prompt = f"{self.system_prompt}\n\n{context_str}Human: {prompt}\nAssistant:"
            
            url = f"{self.base_url}/api/generate"
            payload = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("response", "").strip()
            
        except Exception as e:
            print(f"Completion API error: {e}")
        
        return self._get_fallback_response(prompt)
    
    def _should_search_web(self, prompt: str) -> bool:
        """
        Determine if the prompt requires web search
        
        Args:
            prompt: The user's query
            
        Returns:
            Boolean indicating whether to perform a web search
        """
        if not self.enable_web_search:
            return False
            
        # Check for explicit search requests
        explicit_search_phrases = [
            "search for", "look up", "find information", "search the web",
            "search online", "google", "latest", "recent", "news about",
            "current", "update on", "what is happening with"
        ]
        
        if any(phrase in prompt.lower() for phrase in explicit_search_phrases):
            return True
            
        # Check for questions about current events, facts, or specific information
        question_indicators = [
            "what is", "who is", "where is", "when did", "how does",
            "why does", "what are", "how many", "how much", "what happened",
            "tell me about", "explain", "define", "describe"
        ]
        
        if any(prompt.lower().startswith(q) for q in question_indicators):
            # Don't search for questions about the website itself
            site_related = ["this website", "this site", "this cms", "markdown cms", 
                           "your features", "you work", "you function", "your capabilities"]
            if not any(phrase in prompt.lower() for phrase in site_related):
                return True
                
        # Don't search for content creation or editing requests
        content_related = ["create", "write", "edit", "update", "modify", "post", "article"]
        if any(word in prompt.lower() for word in content_related):
            return False
            
        return False
        
    def _perform_web_search(self, query: str) -> Optional[str]:
        """
        Perform a web search and format results for the LLM
        
        Args:
            query: The search query
            
        Returns:
            Formatted search results or None if search failed
        """
        try:
            # Check if we've recently searched for this query
            query_key = query.lower().strip()
            if query_key in self.recent_searches:
                logger.info(f"Using cached search results for: {query}")
                return self.recent_searches[query_key]
                
            # Perform the search
            search_results = self.web_search.search(query)
            
            if not search_results or not search_results.get("results"):
                logger.warning(f"No search results found for: {query}")
                return None
                
            # Format the results for the LLM
            formatted_results = self.web_search.summarize_search_results(search_results)
            
            # Cache the results (simple cache, could be improved with TTL)
            self.recent_searches[query_key] = formatted_results
            
            # Log the search
            self._log_web_search(query, search_results)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error performing web search for '{query}': {str(e)}")
            return None
            
    def _log_web_search(self, query: str, results: Dict[str, Any]) -> None:
        """Log web search activity"""
        try:
            log_entry = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "result_count": results.get("result_count", 0),
                "response_time": results.get("response_time", 0),
                "urls": [r.get("link") for r in results.get("results", [])]
            }
            
            # Create a unique filename based on timestamp
            filename = f"search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(self.search_log_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(log_entry, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error logging web search: {e}")
    
    def _get_fallback_response(self, prompt: str) -> str:
        """Provide fallback response when LLM is unavailable"""
        # First, try web search if appropriate
        if self._should_search_web(prompt):
            search_results = self._perform_web_search(prompt)
            if search_results:
                return f"""I couldn't connect to my AI brain, but I found some information online that might help:

{search_results}

Note: This information comes from web search results and I couldn't verify its accuracy."""
        
        fallback_responses = [
            "I'm having trouble connecting to my AI brain right now, but I'm still here to help! Could you try asking your question again?",
            "It seems my AI assistant is taking a coffee break! While I get that sorted, feel free to browse the content on this site.",
            "I'm experiencing some technical difficulties at the moment. In the meantime, you might find the blog posts and pages here quite interesting!",
            "My AI capabilities are temporarily offline, but I'd love to chat once they're back up. Have you checked out the latest posts on this site?",
        ]
        
        # Simple keyword-based responses
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return "Hello! Welcome to the site! I'm your friendly talk show host, though I'm having some technical difficulties right now. Feel free to explore the content while I get back online!"
        
        if any(word in prompt_lower for word in ['help', 'what', 'how', 'can you']):
            return "I'd love to help you! I'm currently experiencing some technical issues, but once I'm back online, I can help you discover great content, answer questions, and have engaging conversations about the topics covered on this site."
        
        if any(word in prompt_lower for word in ['bye', 'goodbye', 'see you']):
            return "Thanks for visiting! I hope you found something interesting here. Come back anytime - hopefully my AI will be working better next time!"
        
        # Default fallback
        import random
        return random.choice(fallback_responses)
    
    def _get_conversation_context(self, conversation_id: str) -> List[Dict[str, str]]:
        """Get conversation history"""
        return self.conversations.get(conversation_id, [])
    
    def _store_conversation_message(self, conversation_id: str, role: str, content: str):
        """Store message in conversation history"""
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = []
        
        self.conversations[conversation_id].append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Keep only last 20 messages per conversation
        if len(self.conversations[conversation_id]) > 20:
            self.conversations[conversation_id] = self.conversations[conversation_id][-20:]

    def _enhance_message_for_editing(self, message: str) -> str:
        """Enhance message with context for editing posts"""
        import re

        # Check if this is an edit request
        edit_keywords = ['edit', 'update', 'modify', 'change', 'add to', 'remove from']
        is_edit_request = any(keyword in message.lower() for keyword in edit_keywords)

        if not is_edit_request or not self.content_manager:
            return message

        # Try to extract post reference from message
        post_patterns = [
            r'edit\s+(?:the\s+)?post\s+([a-z0-9-]+)',
            r'update\s+(?:the\s+)?post\s+([a-z0-9-]+)',
            r'modify\s+(?:the\s+)?post\s+([a-z0-9-]+)',
            r'edit\s+(?:the\s+)?([a-z0-9-]+)\s+post',
            r'update\s+(?:the\s+)?([a-z0-9-]+)\s+post',
        ]

        found_slug = None
        for pattern in post_patterns:
            match = re.search(pattern, message.lower())
            if match:
                potential_slug = match.group(1)
                # Check if this slug exists
                if self.content_manager.get_post_by_slug(potential_slug):
                    found_slug = potential_slug
                    break

        # If we found a slug, enhance the message
        if found_slug:
            enhanced = f"{message}\n\nIMPORTANT: This is an EDIT request for existing post with slug '{found_slug}'. You MUST use EDIT_POST format, not CREATE_POST!"
            return enhanced

        # If it's an edit request but no specific post found, list available posts
        if is_edit_request:
            try:
                posts = self.content_manager.get_all_posts()
                if posts:
                    post_list = ", ".join([f"'{post.slug}'" for post in posts[:5]])
                    enhanced = f"{message}\n\nAvailable posts to edit: {post_list}. Use EDIT_POST format with the correct slug."
                    return enhanced
            except:
                pass

        return message

    def get_response(self, message: str, conversation_id: Optional[str] = None) -> str:
        """Get AI response to user message"""
        # Store user message
        if conversation_id:
            self._store_conversation_message(conversation_id, "user", message)

        # Check if we should search the web for this query
        web_search_results = None
        if self._should_search_web(message):
            logger.info(f"Performing web search for query: {message}")
            web_search_results = self._perform_web_search(message)
            
            if web_search_results:
                # Log that we're using web search results
                logger.info(f"Using web search results for: {message}")
                
                # Create an enhanced message with the search results
                enhanced_message = f"""The user asked: {message}

Here is some relevant information from the web that might help you answer:

{web_search_results}

Please use this information to provide a helpful response to the user. Be sure to mention that your answer is based on web search results."""
            else:
                # Enhance message with context for editing
                enhanced_message = self._enhance_message_for_editing(message)
        else:
            # Enhance message with context for editing
            enhanced_message = self._enhance_message_for_editing(message)

        # Get AI response
        response = self._make_request(enhanced_message, conversation_id)
        
        # Check if the response indicates uncertainty
        uncertainty_phrases = [
            "I don't know", "I'm not sure", "I don't have information",
            "I don't have access", "I cannot provide", "I'm unable to",
            "I don't have enough information", "I can't answer",
            "I don't have the ability", "I don't have access to current"
        ]
        
        if any(phrase in response.lower() for phrase in uncertainty_phrases) and not web_search_results:
            # The LLM is uncertain and we haven't already searched
            if self.enable_web_search:
                logger.info(f"LLM expressed uncertainty. Performing web search for: {message}")
                web_search_results = self._perform_web_search(message)
                
                if web_search_results:
                    # Create a new prompt with the search results
                    new_prompt = f"""The user asked: {message}

You initially responded with uncertainty: "{response}"

Here is some relevant information from the web that might help:

{web_search_results}

Please provide a new, more informed response based on this information. Be sure to mention that your answer is based on web search results."""

                    # Get a new response with the search results
                    new_response = self._make_request(new_prompt, conversation_id)
                    
                    # Use the new response if it's not empty
                    if new_response and len(new_response) > 10:
                        response = new_response

        # Process content creation commands
        response = self._process_content_creation(response)

        # Store AI response
        if conversation_id:
            self._store_conversation_message(conversation_id, "assistant", response)

        return response
    
    def is_available(self) -> bool:
        """Check if LLM service is available"""
        try:
            url = f"{self.base_url}/v1/models"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            url = f"{self.base_url}/v1/models"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                data = response.json()
                return [model.get("id", "") for model in data.get("data", [])]
        except:
            pass

        return []
    
    def set_model(self, model_name: str):
        """Change the active model"""
        self.model_name = model_name
    
    def clear_conversation(self, conversation_id: str):
        """Clear conversation history"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation summary"""
        if conversation_id not in self.conversations:
            return {"message_count": 0, "started_at": None, "last_message_at": None}
        
        messages = self.conversations[conversation_id]
        
        return {
            "message_count": len(messages),
            "started_at": messages[0]["timestamp"] if messages else None,
            "last_message_at": messages[-1]["timestamp"] if messages else None
        }
