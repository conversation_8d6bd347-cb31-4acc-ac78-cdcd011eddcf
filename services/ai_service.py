"""
AI Service
Handles communication with local LLM for conversational features
"""

import os
import json
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime

class AIService:
    """Service for interacting with local LLM"""
    
    def __init__(self):
        # Configuration for local LLM (Ollama by default)
        self.base_url = os.environ.get('LLM_BASE_URL', 'http://localhost:11434')
        self.model_name = os.environ.get('LLM_MODEL', 'llama2')
        self.max_tokens = int(os.environ.get('LLM_MAX_TOKENS', '2048'))
        self.temperature = float(os.environ.get('LLM_TEMPERATURE', '0.7'))
        
        # System prompt for the talk show host persona
        self.system_prompt = """You are an engaging and knowledgeable talk show host for this website. Your role is to:

1. Welcome visitors warmly and make them feel comfortable
2. Help them discover interesting content on the site
3. Answer questions about the topics covered in the blog posts and pages
4. Engage in meaningful conversations while staying relevant to the site's content
5. Be conversational, friendly, and slightly entertaining like a good talk show host
6. Ask follow-up questions to keep the conversation flowing
7. Recommend relevant content when appropriate

Keep your responses concise but engaging. Always maintain a positive, helpful tone.
If you don't know something specific about the site's content, be honest about it but try to redirect the conversation in a helpful way."""
        
        # Conversation context storage
        self.conversations = {}
    
    def _make_request(self, prompt: str, conversation_id: Optional[str] = None) -> str:
        """Make request to local LLM"""
        try:
            # Get conversation context
            context = self._get_conversation_context(conversation_id) if conversation_id else []
            
            # Prepare messages for the LLM
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history
            for msg in context[-10:]:  # Keep last 10 messages for context
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Add current user message
            messages.append({"role": "user", "content": prompt})
            
            # Try Ollama API format first
            response = self._try_ollama_api(messages)
            if response:
                return response
            
            # Fallback to simple completion API
            return self._try_completion_api(prompt, context)
            
        except Exception as e:
            print(f"Error communicating with LLM: {e}")
            return self._get_fallback_response(prompt)
    
    def _try_ollama_api(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """Try Ollama chat API format"""
        try:
            url = f"{self.base_url}/api/chat"
            payload = {
                "model": self.model_name,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("message", {}).get("content", "").strip()
            
        except Exception as e:
            print(f"Ollama API error: {e}")
        
        return None
    
    def _try_completion_api(self, prompt: str, context: List[Dict[str, str]]) -> str:
        """Try simple completion API as fallback"""
        try:
            # Build context string
            context_str = ""
            for msg in context[-5:]:  # Last 5 messages
                role = "Human" if msg["role"] == "user" else "Assistant"
                context_str += f"{role}: {msg['content']}\n"
            
            full_prompt = f"{self.system_prompt}\n\n{context_str}Human: {prompt}\nAssistant:"
            
            url = f"{self.base_url}/api/generate"
            payload = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("response", "").strip()
            
        except Exception as e:
            print(f"Completion API error: {e}")
        
        return self._get_fallback_response(prompt)
    
    def _get_fallback_response(self, prompt: str) -> str:
        """Provide fallback response when LLM is unavailable"""
        fallback_responses = [
            "I'm having trouble connecting to my AI brain right now, but I'm still here to help! Could you try asking your question again?",
            "It seems my AI assistant is taking a coffee break! While I get that sorted, feel free to browse the content on this site.",
            "I'm experiencing some technical difficulties at the moment. In the meantime, you might find the blog posts and pages here quite interesting!",
            "My AI capabilities are temporarily offline, but I'd love to chat once they're back up. Have you checked out the latest posts on this site?",
        ]
        
        # Simple keyword-based responses
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return "Hello! Welcome to the site! I'm your friendly talk show host, though I'm having some technical difficulties right now. Feel free to explore the content while I get back online!"
        
        if any(word in prompt_lower for word in ['help', 'what', 'how', 'can you']):
            return "I'd love to help you! I'm currently experiencing some technical issues, but once I'm back online, I can help you discover great content, answer questions, and have engaging conversations about the topics covered on this site."
        
        if any(word in prompt_lower for word in ['bye', 'goodbye', 'see you']):
            return "Thanks for visiting! I hope you found something interesting here. Come back anytime - hopefully my AI will be working better next time!"
        
        # Default fallback
        import random
        return random.choice(fallback_responses)
    
    def _get_conversation_context(self, conversation_id: str) -> List[Dict[str, str]]:
        """Get conversation history"""
        return self.conversations.get(conversation_id, [])
    
    def _store_conversation_message(self, conversation_id: str, role: str, content: str):
        """Store message in conversation history"""
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = []
        
        self.conversations[conversation_id].append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Keep only last 20 messages per conversation
        if len(self.conversations[conversation_id]) > 20:
            self.conversations[conversation_id] = self.conversations[conversation_id][-20:]
    
    def get_response(self, message: str, conversation_id: Optional[str] = None) -> str:
        """Get AI response to user message"""
        # Store user message
        if conversation_id:
            self._store_conversation_message(conversation_id, "user", message)
        
        # Get AI response
        response = self._make_request(message, conversation_id)
        
        # Store AI response
        if conversation_id:
            self._store_conversation_message(conversation_id, "assistant", response)
        
        return response
    
    def is_available(self) -> bool:
        """Check if LLM service is available"""
        try:
            url = f"{self.base_url}/api/tags"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            url = f"{self.base_url}/api/tags"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return [model.get("name", "") for model in data.get("models", [])]
        except:
            pass
        
        return []
    
    def set_model(self, model_name: str):
        """Change the active model"""
        self.model_name = model_name
    
    def clear_conversation(self, conversation_id: str):
        """Clear conversation history"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation summary"""
        if conversation_id not in self.conversations:
            return {"message_count": 0, "started_at": None, "last_message_at": None}
        
        messages = self.conversations[conversation_id]
        
        return {
            "message_count": len(messages),
            "started_at": messages[0]["timestamp"] if messages else None,
            "last_message_at": messages[-1]["timestamp"] if messages else None
        }
