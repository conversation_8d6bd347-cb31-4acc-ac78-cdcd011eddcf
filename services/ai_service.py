"""
AI Service
Handles communication with local LLM for conversational features
"""

import os
import json
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime

class AIService:
    """Service for interacting with local LLM"""

    def __init__(self, content_manager=None):
        # Configuration for local LLM (LM Studio by default)
        self.base_url = os.environ.get('LLM_BASE_URL', 'http://localhost:1234')
        self.model_name = os.environ.get('LLM_MODEL', 'microsoft/phi-4-mini-reasoning')
        self.max_tokens = int(os.environ.get('LLM_MAX_TOKENS', '2048'))
        self.temperature = float(os.environ.get('LLM_TEMPERATURE', '0.7'))

        # Content manager for creating posts
        self.content_manager = content_manager
        
        # System prompt for the talk show host persona
        self.system_prompt = """You are a friendly website assistant. You help visitors and can create content.

CONTENT CREATION:
When users say "create a post" or "write an article", use this EXACT format:

[CREATE_POST]
Title: Your Post Title Here
Tags: tag1, tag2, tag3
Published: false
Content:
# Your Post Title Here

Write your content here in markdown format.

## Section 1
Content goes here...

## Section 2
More content...
[/CREATE_POST]

For pages, use [CREATE_PAGE] instead of [CREATE_POST].

EDITING CONTENT:
When users ask to "edit a post", "update an article", or "modify content", use this format:

[EDIT_POST]
Slug: existing-post-slug
Title: Updated Title (optional)
Tags: new, tags, here (optional)
Published: true (optional)
Content:
# Updated content here

Your updated markdown content...
[/EDIT_POST]

LISTING POSTS:
When users ask "what posts exist" or "list posts", use this format:

[LIST_POSTS]
[/LIST_POSTS]

RULES:
- Keep responses short (1-2 sentences) unless creating/editing content
- Always use the exact format above for content creation/editing
- For editing, you can update any field or just the content
- Be helpful and friendly"""
        
        # Conversation context storage
        self.conversations = {}
    
    def _make_request(self, prompt: str, conversation_id: Optional[str] = None) -> str:
        """Make request to local LLM"""
        try:
            # Get conversation context
            context = self._get_conversation_context(conversation_id) if conversation_id else []
            
            # Prepare messages for the LLM
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history
            for msg in context[-10:]:  # Keep last 10 messages for context
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Add current user message
            messages.append({"role": "user", "content": prompt})
            
            # Try LM Studio API format first
            response = self._try_lmstudio_api(messages)
            if response:
                return response

            # Fallback to simple completion API
            return self._try_completion_api(prompt, context)
            
        except Exception as e:
            print(f"Error communicating with LLM: {e}")
            return self._get_fallback_response(prompt)
    
    def _try_lmstudio_api(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """Try LM Studio OpenAI-compatible API format"""
        try:
            url = f"{self.base_url}/v1/chat/completions"
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": False
            }

            response = requests.post(url, json=payload, timeout=60)

            if response.status_code == 200:
                data = response.json()
                content = data.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
                # Filter out thinking process and return only the actual response
                return self._clean_response(content)

        except Exception as e:
            print(f"LM Studio API error: {e}")

        return None

    def _clean_response(self, content: str) -> str:
        """Clean the AI response by removing thinking process and formatting"""
        import re

        # Remove <think>...</think> blocks
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

        # Remove any remaining XML-like tags
        content = re.sub(r'<[^>]+>', '', content)

        # Clean up extra whitespace and newlines
        content = re.sub(r'\n\s*\n', '\n\n', content)  # Multiple newlines to double
        content = content.strip()

        # If the response is empty after cleaning, provide a fallback
        if not content:
            content = "I'm here to help! What would you like to know about this site?"

        return content

    def _process_content_creation(self, response: str) -> str:
        """Process content creation commands in AI response"""
        if not self.content_manager:
            return response

        import re

        print(f"DEBUG: Processing AI response for content creation: {response[:200]}...")

        # Check for CREATE_POST command
        post_match = re.search(r'\[CREATE_POST\](.*?)\[/CREATE_POST\]', response, re.DOTALL)
        if post_match:
            print("DEBUG: Found CREATE_POST command")
            try:
                post_content = post_match.group(1).strip()
                post_data = self._parse_content_creation(post_content)

                # Create the post
                post = self.content_manager.create_post(
                    title=post_data['title'],
                    content=post_data['content'],
                    tags=post_data.get('tags', []),
                    published=post_data.get('published', False)
                )

                # Replace the command with success message
                success_msg = f"✅ I've created the post '{post.title}' for you! You can view it at /post/{post.slug} or edit it in the admin panel."
                response = response.replace(post_match.group(0), success_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't create the post. Error: {str(e)}"
                response = response.replace(post_match.group(0), error_msg)

        # Check for CREATE_PAGE command
        page_match = re.search(r'\[CREATE_PAGE\](.*?)\[/CREATE_PAGE\]', response, re.DOTALL)
        if page_match:
            try:
                page_content = page_match.group(1).strip()
                page_data = self._parse_content_creation(page_content)

                # Create the page
                page = self.content_manager.create_page(
                    title=page_data['title'],
                    content=page_data['content'],
                    published=page_data.get('published', False)
                )

                # Replace the command with success message
                success_msg = f"✅ I've created the page '{page.title}' for you! You can view it at /page/{page.slug} or edit it in the admin panel."
                response = response.replace(page_match.group(0), success_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't create the page. Error: {str(e)}"
                response = response.replace(page_match.group(0), error_msg)

        # Check for EDIT_POST command
        edit_match = re.search(r'\[EDIT_POST\](.*?)\[/EDIT_POST\]', response, re.DOTALL)
        if edit_match:
            print("DEBUG: Found EDIT_POST command")
            try:
                edit_content = edit_match.group(1).strip()
                edit_data = self._parse_edit_command(edit_content)

                # Get the existing post
                post = self.content_manager.get_post_by_slug(edit_data['slug'])
                if not post:
                    error_msg = f"❌ Sorry, I couldn't find a post with slug '{edit_data['slug']}'. Please check the post name."
                    response = response.replace(edit_match.group(0), error_msg)
                else:
                    # Update the post
                    updated_post = self.content_manager.update_post(
                        slug=edit_data['slug'],
                        title=edit_data.get('title', post.title),
                        content=edit_data.get('content', post.content),
                        tags=edit_data.get('tags', post.tags),
                        published=edit_data.get('published', post.published)
                    )

                    # Replace the command with success message
                    success_msg = f"✅ I've updated the post '{updated_post.title}'! You can view it at /post/{updated_post.slug} or edit it further in the admin panel."
                    response = response.replace(edit_match.group(0), success_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't edit the post. Error: {str(e)}"
                response = response.replace(edit_match.group(0), error_msg)

        # Check for LIST_POSTS command
        list_match = re.search(r'\[LIST_POSTS\](.*?)\[/LIST_POSTS\]', response, re.DOTALL)
        if list_match:
            print("DEBUG: Found LIST_POSTS command")
            try:
                posts = self.content_manager.get_all_posts()

                if not posts:
                    list_msg = "📝 No posts found. You can create your first post by asking me to 'create a post about [topic]'."
                else:
                    list_msg = "📝 **Available Posts:**\n\n"
                    for post in posts[:10]:  # Limit to 10 posts
                        status = "✅ Published" if post.published else "📝 Draft"
                        list_msg += f"• **{post.title}** (slug: `{post.slug}`) - {status}\n"

                    if len(posts) > 10:
                        list_msg += f"\n...and {len(posts) - 10} more posts. You can edit any post by saying 'edit the post [slug]' or 'update the [title] post'."
                    else:
                        list_msg += "\nYou can edit any post by saying 'edit the post [slug]' or 'update the [title] post'."

                response = response.replace(list_match.group(0), list_msg)

            except Exception as e:
                error_msg = f"❌ Sorry, I couldn't list the posts. Error: {str(e)}"
                response = response.replace(list_match.group(0), error_msg)

        return response

    def _parse_content_creation(self, content: str) -> Dict[str, Any]:
        """Parse content creation command"""
        lines = content.split('\n')
        data = {
            'title': 'Untitled',
            'content': '',
            'tags': [],
            'published': False
        }

        content_started = False
        content_lines = []

        for line in lines:
            line = line.strip()

            if line.startswith('Title:'):
                data['title'] = line.replace('Title:', '').strip()
            elif line.startswith('Tags:'):
                tags_str = line.replace('Tags:', '').strip()
                if tags_str:
                    # Parse tags - handle both comma and space separated
                    tags = [tag.strip().strip('[]') for tag in tags_str.replace(',', ' ').split()]
                    data['tags'] = [tag for tag in tags if tag]
            elif line.startswith('Published:'):
                pub_str = line.replace('Published:', '').strip().lower()
                data['published'] = pub_str in ['true', 'yes', '1']
            elif line.startswith('Content:'):
                content_started = True
            elif content_started:
                content_lines.append(line)

        # Join content lines
        if content_lines:
            data['content'] = '\n'.join(content_lines).strip()

        return data

    def _parse_edit_command(self, content: str) -> Dict[str, Any]:
        """Parse edit command content"""
        lines = content.split('\n')
        data = {
            'slug': None,
            'title': None,
            'content': None,
            'tags': None,
            'published': None
        }

        content_started = False
        content_lines = []

        for line in lines:
            line = line.strip()

            if line.startswith('Slug:'):
                data['slug'] = line.replace('Slug:', '').strip()
            elif line.startswith('Title:'):
                data['title'] = line.replace('Title:', '').strip()
            elif line.startswith('Tags:'):
                tags_str = line.replace('Tags:', '').strip()
                if tags_str:
                    # Parse tags - handle both comma and space separated
                    tags = [tag.strip().strip('[]') for tag in tags_str.replace(',', ' ').split()]
                    data['tags'] = [tag for tag in tags if tag]
            elif line.startswith('Published:'):
                pub_str = line.replace('Published:', '').strip().lower()
                data['published'] = pub_str in ['true', 'yes', '1']
            elif line.startswith('Content:'):
                content_started = True
            elif content_started:
                content_lines.append(line)

        # Join content lines
        if content_lines:
            data['content'] = '\n'.join(content_lines).strip()

        # Remove None values to only update specified fields
        data = {k: v for k, v in data.items() if v is not None}

        return data

    def _try_completion_api(self, prompt: str, context: List[Dict[str, str]]) -> str:
        """Try simple completion API as fallback"""
        try:
            # Build context string
            context_str = ""
            for msg in context[-5:]:  # Last 5 messages
                role = "Human" if msg["role"] == "user" else "Assistant"
                context_str += f"{role}: {msg['content']}\n"
            
            full_prompt = f"{self.system_prompt}\n\n{context_str}Human: {prompt}\nAssistant:"
            
            url = f"{self.base_url}/api/generate"
            payload = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("response", "").strip()
            
        except Exception as e:
            print(f"Completion API error: {e}")
        
        return self._get_fallback_response(prompt)
    
    def _get_fallback_response(self, prompt: str) -> str:
        """Provide fallback response when LLM is unavailable"""
        fallback_responses = [
            "I'm having trouble connecting to my AI brain right now, but I'm still here to help! Could you try asking your question again?",
            "It seems my AI assistant is taking a coffee break! While I get that sorted, feel free to browse the content on this site.",
            "I'm experiencing some technical difficulties at the moment. In the meantime, you might find the blog posts and pages here quite interesting!",
            "My AI capabilities are temporarily offline, but I'd love to chat once they're back up. Have you checked out the latest posts on this site?",
        ]
        
        # Simple keyword-based responses
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return "Hello! Welcome to the site! I'm your friendly talk show host, though I'm having some technical difficulties right now. Feel free to explore the content while I get back online!"
        
        if any(word in prompt_lower for word in ['help', 'what', 'how', 'can you']):
            return "I'd love to help you! I'm currently experiencing some technical issues, but once I'm back online, I can help you discover great content, answer questions, and have engaging conversations about the topics covered on this site."
        
        if any(word in prompt_lower for word in ['bye', 'goodbye', 'see you']):
            return "Thanks for visiting! I hope you found something interesting here. Come back anytime - hopefully my AI will be working better next time!"
        
        # Default fallback
        import random
        return random.choice(fallback_responses)
    
    def _get_conversation_context(self, conversation_id: str) -> List[Dict[str, str]]:
        """Get conversation history"""
        return self.conversations.get(conversation_id, [])
    
    def _store_conversation_message(self, conversation_id: str, role: str, content: str):
        """Store message in conversation history"""
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = []
        
        self.conversations[conversation_id].append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Keep only last 20 messages per conversation
        if len(self.conversations[conversation_id]) > 20:
            self.conversations[conversation_id] = self.conversations[conversation_id][-20:]
    
    def get_response(self, message: str, conversation_id: Optional[str] = None) -> str:
        """Get AI response to user message"""
        # Store user message
        if conversation_id:
            self._store_conversation_message(conversation_id, "user", message)

        # Get AI response
        response = self._make_request(message, conversation_id)

        # Process content creation commands
        response = self._process_content_creation(response)

        # Store AI response
        if conversation_id:
            self._store_conversation_message(conversation_id, "assistant", response)

        return response
    
    def is_available(self) -> bool:
        """Check if LLM service is available"""
        try:
            url = f"{self.base_url}/v1/models"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            url = f"{self.base_url}/v1/models"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                data = response.json()
                return [model.get("id", "") for model in data.get("data", [])]
        except:
            pass

        return []
    
    def set_model(self, model_name: str):
        """Change the active model"""
        self.model_name = model_name
    
    def clear_conversation(self, conversation_id: str):
        """Clear conversation history"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation summary"""
        if conversation_id not in self.conversations:
            return {"message_count": 0, "started_at": None, "last_message_at": None}
        
        messages = self.conversations[conversation_id]
        
        return {
            "message_count": len(messages),
            "started_at": messages[0]["timestamp"] if messages else None,
            "last_message_at": messages[-1]["timestamp"] if messages else None
        }
