"""
Web Search Service
Provides internet search capabilities for the AI service
"""

import os
import json
import requests
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from urllib.parse import quote_plus

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSearchService:
    """Service for performing web searches and processing results"""
    
    def __init__(self):
        # Load API keys from environment variables
        self.bing_api_key = os.environ.get('BING_SEARCH_API_KEY', '')
        self.serp_api_key = os.environ.get('SERP_API_KEY', '')
        self.google_api_key = os.environ.get('GOOGLE_SEARCH_API_KEY', '')
        self.google_cx = os.environ.get('GOOGLE_SEARCH_CX', '')
        
        # Configure search settings
        self.max_results = int(os.environ.get('SEARCH_MAX_RESULTS', '5'))
        self.timeout = int(os.environ.get('SEARCH_TIMEOUT_SECONDS', '10'))
        self.search_log_dir = os.environ.get('SEARCH_LOG_DIR', 'data/search_logs')
        
        # Create log directory if it doesn't exist
        os.makedirs(self.search_log_dir, exist_ok=True)
        
        # Determine which search API to use based on available keys
        if self.bing_api_key:
            self.primary_search_method = self._search_bing
        elif self.google_api_key and self.google_cx:
            self.primary_search_method = self._search_google
        elif self.serp_api_key:
            self.primary_search_method = self._search_serpapi
        else:
            self.primary_search_method = self._search_duckduckgo
            logger.warning("No search API keys configured. Using DuckDuckGo as fallback (limited functionality).")
    
    def search(self, query: str) -> Dict[str, Any]:
        """
        Perform a web search and return formatted results
        
        Args:
            query: The search query string
            
        Returns:
            Dictionary containing search results and metadata
        """
        start_time = datetime.now()
        
        try:
            # Log the search query
            logger.info(f"Performing web search for: {query}")
            
            # Execute the primary search method
            results = self.primary_search_method(query)
            
            # Calculate response time
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Format the search results
            formatted_results = self._format_results(query, results, response_time)
            
            # Log the search activity
            self._log_search_activity(query, formatted_results)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search error for query '{query}': {str(e)}")
            
            # Return empty results on error
            return {
                "query": query,
                "results": [],
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "response_time": (datetime.now() - start_time).total_seconds()
            }
    
    def _search_bing(self, query: str) -> List[Dict[str, Any]]:
        """Search using Bing Search API"""
        if not self.bing_api_key:
            raise ValueError("Bing Search API key not configured")
        
        url = "https://api.bing.microsoft.com/v7.0/search"
        headers = {"Ocp-Apim-Subscription-Key": self.bing_api_key}
        params = {
            "q": query,
            "count": self.max_results,
            "responseFilter": "Webpages",
            "textDecorations": False,
            "textFormat": "Raw"
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=self.timeout)
        response.raise_for_status()
        
        search_data = response.json()
        
        results = []
        if "webPages" in search_data and "value" in search_data["webPages"]:
            for item in search_data["webPages"]["value"]:
                results.append({
                    "title": item.get("name", ""),
                    "link": item.get("url", ""),
                    "snippet": item.get("snippet", ""),
                    "source": "Bing"
                })
        
        return results
    
    def _search_google(self, query: str) -> List[Dict[str, Any]]:
        """Search using Google Programmable Search API"""
        if not self.google_api_key or not self.google_cx:
            raise ValueError("Google Search API key or CX not configured")
        
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.google_api_key,
            "cx": self.google_cx,
            "q": query,
            "num": self.max_results
        }
        
        response = requests.get(url, params=params, timeout=self.timeout)
        response.raise_for_status()
        
        search_data = response.json()
        
        results = []
        if "items" in search_data:
            for item in search_data["items"]:
                results.append({
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "source": "Google"
                })
        
        return results
    
    def _search_serpapi(self, query: str) -> List[Dict[str, Any]]:
        """Search using SerpAPI"""
        if not self.serp_api_key:
            raise ValueError("SerpAPI key not configured")
        
        url = "https://serpapi.com/search"
        params = {
            "api_key": self.serp_api_key,
            "q": query,
            "num": self.max_results,
            "engine": "google"
        }
        
        response = requests.get(url, params=params, timeout=self.timeout)
        response.raise_for_status()
        
        search_data = response.json()
        
        results = []
        if "organic_results" in search_data:
            for item in search_data["organic_results"]:
                results.append({
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "source": "SerpAPI"
                })
        
        return results
    
    def _search_duckduckgo(self, query: str) -> List[Dict[str, Any]]:
        """
        Search using DuckDuckGo (fallback method with limited functionality)
        Note: This is a basic implementation as DuckDuckGo doesn't have an official API
        """
        # DuckDuckGo HTML search (not an official API)
        url = f"https://html.duckduckgo.com/html/?q={quote_plus(query)}"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # Very basic HTML parsing (not reliable for production)
            # In a production environment, use a proper HTML parser like BeautifulSoup
            results = []
            content = response.text
            
            # Extract results (this is a simplified approach)
            result_blocks = content.split('<div class="result__body')
            
            for i, block in enumerate(result_blocks[1:self.max_results+1]):
                try:
                    # Extract title
                    title_start = block.find('class="result__a">')
                    title_end = block.find('</a>', title_start)
                    title = block[title_start+19:title_end].strip()
                    title = title.replace('<b>', '').replace('</b>', '')
                    
                    # Extract URL
                    url_start = block.find('href="') + 6
                    url_end = block.find('"', url_start)
                    url = block[url_start:url_end].strip()
                    
                    # Extract snippet
                    snippet_start = block.find('class="result__snippet">')
                    snippet_end = block.find('</a>', snippet_start)
                    snippet = block[snippet_start+24:snippet_end].strip()
                    snippet = snippet.replace('<b>', '').replace('</b>', '')
                    
                    if title and url:
                        results.append({
                            "title": title,
                            "link": url,
                            "snippet": snippet,
                            "source": "DuckDuckGo"
                        })
                except Exception as e:
                    logger.error(f"Error parsing DuckDuckGo result: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"DuckDuckGo search error: {e}")
            return []
    
    def _format_results(self, query: str, results: List[Dict[str, Any]], response_time: float) -> Dict[str, Any]:
        """Format search results with metadata"""
        return {
            "query": query,
            "results": results,
            "result_count": len(results),
            "timestamp": datetime.now().isoformat(),
            "response_time": response_time
        }
    
    def _log_search_activity(self, query: str, results: Dict[str, Any]) -> None:
        """Log search activity to file"""
        try:
            log_entry = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "result_count": results.get("result_count", 0),
                "response_time": results.get("response_time", 0),
                "urls": [r.get("link") for r in results.get("results", [])]
            }
            
            # Create a unique filename based on timestamp
            filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(query) % 10000}.json"
            filepath = os.path.join(self.search_log_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(log_entry, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error logging search activity: {e}")
    
    def summarize_search_results(self, results: Dict[str, Any]) -> str:
        """
        Create a concise summary of search results for the LLM
        
        Args:
            results: The search results dictionary
            
        Returns:
            A formatted string summary of the search results
        """
        if not results or not results.get("results"):
            return "No search results found."
        
        summary = f"Search results for: {results['query']}\n\n"
        
        for i, result in enumerate(results["results"], 1):
            summary += f"{i}. {result['title']}\n"
            summary += f"   URL: {result['link']}\n"
            summary += f"   {result['snippet']}\n\n"
        
        summary += f"Information retrieved from the web at {results['timestamp']}."
        
        return summary