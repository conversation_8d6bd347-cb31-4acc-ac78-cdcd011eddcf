---
excerpt: ' How to Create a Bulleted List in Markdown To create a bulleted list in
  Markdown, you can use the following syntax: - Use - hyphen or  asterisk. -...'
published: false
tags:
- markdown
- lists
- formatting
title: How to Create a Bulleted List in Markdown
---

# How to Create a Bulleted List in Markdown

To create a bulleted list in Markdown, you can use the following syntax:

- Use `-` (hyphen) or `*` (asterisk).
- Indent each line of text by two spaces after the bullet.

Here are some examples:

#### Using Hyphens (`-`)
1. This is an item.
2. Another item with a longer description.
3. Yet another item.

#### Using Asterisks (`*`)
* Item 1
* Item 2, which includes multiple lines and can be indented for better readability.
* Sub-item within the second main item

### Example with Both:
- First bullet using `-`
- Second bullet using `*`

### More Examples:
Here is a more complex example that shows how to nest items under each other:

1. Primary list item
- Secondary item 1 (nested)
- Tertiary item 1
- Tertiary item 2
- Secondary item 2

2. Primary list item 2 (not nested)

### Note:
- Make sure your bullet points are indented by two spaces to maintain the correct format.