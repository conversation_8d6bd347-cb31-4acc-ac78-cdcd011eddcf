---
title: About This CMS
published: true
menu_order: 1
---

# About Markdown CMS

This is a modern, **conversational content management system** that puts simplicity and engagement at the forefront. Built for content creators who want to focus on writing while providing visitors with an interactive, AI-enhanced experience.

## Our Philosophy

We believe that content management should be:

- **Simple**: Write in Markdown, publish instantly
- **Fast**: No database overhead, just files
- **Interactive**: AI-powered conversations enhance the user experience
- **Flexible**: Easy to customize and extend

## Key Features

### Content Management
- **Markdown-based**: Write content in the format you love
- **File-based storage**: No complex database setup required
- **Version control friendly**: Track changes with Git
- **Media support**: Easy file uploads and management

### AI Integration
- **Local LLM support**: Works with Ollama, LM Studio, and other local AI services
- **Conversational interface**: AI acts as a friendly talk show host
- **Context-aware**: AI understands your content and can discuss it intelligently
- **Privacy-focused**: All AI processing happens locally

### Technical Excellence
- **Python/Flask backend**: Reliable and well-documented
- **Bootstrap frontend**: Responsive and accessible design
- **RESTful API**: Easy integration with other tools
- **Extensible architecture**: Add features as you need them

## Who Is This For?

This CMS is perfect for:

- **Bloggers** who want a simple, fast platform
- **Documentation sites** that need AI-assisted help
- **Personal websites** with interactive features
- **Small businesses** wanting engaging customer interactions
- **Developers** who prefer file-based content management

## Technology Stack

- **Backend**: Python 3.8+, Flask, Markdown processing
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **AI**: Local LLM integration (Ollama recommended)
- **Storage**: File-based (Markdown files + JSON metadata)
- **Deployment**: Can run anywhere Python runs

## Getting Support

Need help? Our AI chat feature is designed to assist you! Try asking:

- "How do I create a new post?"
- "What Markdown features are supported?"
- "How does the AI chat work?"
- "Can you help me customize the design?"

The AI host has been trained to understand this CMS and can provide helpful guidance for most common tasks.

## Contributing

This is an open-source project built with love for the community. Whether you're fixing bugs, adding features, or improving documentation, contributions are welcome!

## License

This project is released under the MIT License, making it free to use, modify, and distribute for both personal and commercial projects.

---

*Ready to start creating? Head over to the [admin panel](/admin) to begin building your content library!*
