#!/usr/bin/env python3
"""
Test script for web search functionality
"""

import os
import sys
import json
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.web_search_service import WebSearchService
from services.ai_service import AIService
from services.content_manager import ContentManager

def test_web_search_service():
    """Test the WebSearchService directly"""
    print("\n=== Testing WebSearchService ===")
    
    # Initialize the service
    search_service = WebSearchService()
    
    # Test queries
    test_queries = [
        "What is the capital of France?",
        "Latest news about artificial intelligence",
        "Who won the most recent Super Bowl?",
        "Current weather in New York"
    ]
    
    for query in test_queries:
        print(f"\nSearching for: {query}")
        results = search_service.search(query)
        
        print(f"Found {results.get('result_count', 0)} results in {results.get('response_time', 0):.2f} seconds")
        
        if results.get('results'):
            for i, result in enumerate(results['results'][:3], 1):
                print(f"{i}. {result['title']}")
                print(f"   URL: {result['link']}")
                print(f"   {result['snippet'][:100]}...")
        else:
            print("No results found or search failed")
            
        # Test summarization
        if results.get('results'):
            print("\nSummarized results:")
            summary = search_service.summarize_search_results(results)
            print(summary[:300] + "..." if len(summary) > 300 else summary)

def test_ai_service_with_search():
    """Test the AIService with web search integration"""
    print("\n=== Testing AIService with Web Search ===")
    
    # Initialize services
    content_manager = ContentManager()
    ai_service = AIService(content_manager)
    
    # Ensure web search is enabled
    ai_service.enable_web_search = True
    
    # Test queries that should trigger web search
    test_queries = [
        "What is the latest news about climate change?",
        "Who is the current CEO of Microsoft?",
        "What are the symptoms of COVID-19?",
        "When was the last Olympics held?"
    ]
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        
        # Check if query should trigger search
        should_search = ai_service._should_search_web(query)
        print(f"Should trigger search: {should_search}")
        
        if should_search:
            # Test direct search
            search_results = ai_service._perform_web_search(query)
            if search_results:
                print(f"Search successful, got {len(search_results.split('\\n'))} lines of results")
                print(search_results[:200] + "..." if len(search_results) > 200 else search_results)
            else:
                print("Search failed or no results")
        
        # Test full response flow
        print("\nGetting AI response...")
        response = ai_service.get_response(query)
        print(f"Response: {response[:200]}..." if len(response) > 200 else response)

def main():
    """Run all tests"""
    print(f"=== Web Search Tests === {datetime.now().isoformat()}")
    
    # Test the WebSearchService
    test_web_search_service()
    
    # Test the AIService with web search
    test_ai_service_with_search()
    
    print("\n=== Tests Complete ===")

if __name__ == "__main__":
    main()