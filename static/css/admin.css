/* Admin Panel CSS for Markdown CMS */

/* Sidebar */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* Main content */
main {
    margin-left: 240px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    
    main {
        margin-left: 0;
    }
}

/* Cards with borders */
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}

.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}

.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}

/* Editor Styles */
.editor-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

.editor-toolbar {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
}

.editor-content {
    position: relative;
}

.editor-content textarea {
    border: none;
    border-radius: 0;
    resize: vertical;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.editor-content textarea:focus {
    box-shadow: none;
    border: none;
}

.preview-content {
    padding: 1rem;
    background-color: white;
    min-height: 500px;
    border: none;
    font-family: inherit;
    line-height: 1.6;
}

.preview-content h1,
.preview-content h2,
.preview-content h3,
.preview-content h4,
.preview-content h5,
.preview-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.preview-content h1 {
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
}

.preview-content h2 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.25rem;
}

.preview-content code {
    background-color: #f8f9fa;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

.preview-content pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    overflow-x: auto;
}

.preview-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 0.375rem 0.375rem 0;
}

/* Tags Input */
.tags-container {
    min-height: 2.5rem;
    padding: 0.375rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: white;
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

.tag-item {
    background-color: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.tag-item .remove-tag {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.75rem;
}

.tag-item .remove-tag:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* File Upload */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

/* Tables */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Buttons */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Stats Cards */
.stat-item h4,
.stat-item h5,
.stat-item h6 {
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-item small {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin-left: -0.5rem;
    margin-top: -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Enhancements */
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .editor-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .editor-toolbar .btn-group {
        justify-content: center;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .sidebar {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .sidebar .nav-link {
        color: #e2e8f0;
    }
    
    .sidebar .nav-link:hover {
        color: #63b3ed;
        background-color: rgba(99, 179, 237, 0.1);
    }
    
    .sidebar .nav-link.active {
        color: #63b3ed;
        background-color: rgba(99, 179, 237, 0.1);
    }
    
    .editor-toolbar {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .preview-content {
        background-color: #1a202c;
        color: #e2e8f0;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Fullscreen Editor */
.fullscreen-editor {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    background: white !important;
    border-radius: 0 !important;
    overflow: hidden !important;
}

.fullscreen-editor .editor-content textarea {
    min-height: calc(100vh - 120px) !important;
    resize: none !important;
}

.fullscreen-editor .preview-content {
    min-height: calc(100vh - 120px) !important;
}

.editor-fullscreen {
    overflow: hidden !important;
}

.editor-fullscreen .navbar,
.editor-fullscreen .sidebar {
    z-index: 9998 !important;
}

/* Escape key hint for fullscreen */
.fullscreen-editor::before {
    content: "Press ESC to exit fullscreen";
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10000;
    opacity: 0;
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    20%, 80% { opacity: 1; }
}

/* Voice Assistant Styles for Admin */
.voice-assistant {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    text-align: center;
}

.assistant-indicator {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.assistant-indicator:hover {
    transform: scale(1.1);
}

.assistant-indicator.listening {
    animation: pulse 2s infinite;
}

.pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border: 3px solid #007bff;
    border-radius: 50%;
    opacity: 0;
}

.assistant-indicator.listening .pulse-ring {
    animation: pulse-ring 2s infinite;
}

.assistant-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.assistant-status {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.voice-assistant:hover .assistant-status {
    opacity: 1;
}

/* Voice Animation */
.voice-animation {
    width: 100px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
}

.voice-wave {
    width: 4px;
    height: 20px;
    background: linear-gradient(to top, #007bff, #0056b3);
    border-radius: 2px;
    animation: voice-wave 1.5s ease-in-out infinite;
}

.voice-wave:nth-child(2) { animation-delay: 0.1s; }
.voice-wave:nth-child(3) { animation-delay: 0.2s; }
.voice-wave:nth-child(4) { animation-delay: 0.3s; }
.voice-wave:nth-child(5) { animation-delay: 0.4s; }

/* Assistant Suggestions */
.assistant-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.suggestion-chip {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
}

.suggestion-chip:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateY(-2px);
}

/* Voice Assistant Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes pulse-ring {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 0;
    }
}

@keyframes voice-wave {
    0%, 100% { height: 20px; }
    50% { height: 40px; }
}

/* Voice Assistant Responsive for Admin */
@media (max-width: 768px) {
    .voice-assistant {
        bottom: 80px;
        right: 15px;
    }

    .assistant-indicator {
        width: 50px;
        height: 50px;
    }

    .assistant-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .pulse-ring {
        width: 50px;
        height: 50px;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .editor-toolbar,
    .voice-assistant {
        display: none !important;
    }

    main {
        margin-left: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
