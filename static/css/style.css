/* Main CSS for Markdown CMS */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.content-area {
    min-height: calc(100vh - 200px);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
}

/* Chat Interface */
.chat-container {
    position: sticky;
    top: 20px;
}

.chat-messages {
    background-color: #f8f9fa;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.message {
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease-in;
}

.message-content {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 85%;
    word-wrap: break-word;
}

.user-message .message-content {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.ai-message .message-content {
    background-color: white;
    border: 1px solid #e9ecef;
    margin-right: auto;
}

.message-timestamp {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 5px;
}

.chat-input {
    background-color: white;
}

#chat-input {
    border: 1px solid #ced4da;
    border-radius: 20px;
    padding: 10px 15px;
}

#chat-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#send-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Content Styling */
.hero-section .jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-section .jumbotron .btn {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.hero-section .jumbotron .btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Post and Page Content */
.post-content, .page-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.post-title, .page-title {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.post-meta, .page-meta {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.post-body, .page-body {
    margin-top: 2rem;
}

.post-body h1, .post-body h2, .post-body h3, 
.page-body h1, .page-body h2, .page-body h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.post-body h1, .page-body h1 {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.post-body h2, .page-body h2 {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.3rem;
}

.post-body blockquote, .page-body blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.post-body code, .page-body code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #e83e8c;
}

.post-body pre, .page-body pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    border: 1px solid #e9ecef;
}

.post-body pre code, .page-body pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

.post-body img, .page-body img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

.post-body table, .page-body table {
    width: 100%;
    margin: 1.5rem 0;
    border-collapse: collapse;
}

.post-body table th, .post-body table td,
.page-body table th, .page-body table td {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    text-align: left;
}

.post-body table th, .page-body table th {
    background-color: var(--light-color);
    font-weight: 600;
}

/* Tags */
.tags .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* Sidebar */
.sidebar {
    background-color: #f8f9fa;
    min-height: calc(100vh - 56px);
}

.recent-posts .list-group-item {
    border: none;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.recent-posts .list-group-item:last-child {
    border-bottom: none;
}

/* Table of Contents */
.toc-container ul {
    list-style: none;
    padding-left: 0;
}

.toc-container ul ul {
    padding-left: 1rem;
}

.toc-container a {
    color: #6c757d;
    text-decoration: none;
    display: block;
    padding: 0.25rem 0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toc-container a:hover {
    color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
    padding-left: 0.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(13, 110, 253, 0.1);
    border-bottom: 1px solid rgba(13, 110, 253, 0.2);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0b5ed7);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0a58ca);
}

/* Features Section */
.features-section .card {
    transition: transform 0.3s ease;
}

.features-section .card:hover {
    transform: translateY(-5px);
}

/* Footer */
footer {
    margin-top: auto;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-area {
        padding: 1rem !important;
    }
    
    .sidebar {
        padding: 1rem !important;
    }
    
    .post-title, .page-title {
        font-size: 2rem;
    }
    
    .hero-section .jumbotron {
        padding: 2rem !important;
    }
    
    .chat-messages {
        height: 250px;
    }
    
    .message-content {
        max-width: 95%;
    }
}

@media (max-width: 576px) {
    .post-title, .page-title {
        font-size: 1.5rem;
    }
    
    .post-meta, .page-meta {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .post-meta > div, .page-meta > div {
        margin-bottom: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .sidebar, .chat-container, .admin-actions, .share-buttons {
        display: none !important;
    }
    
    .content-area {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .post-content, .page-content {
        max-width: none !important;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Markdown Source View */
.markdown-source {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
    max-height: 70vh;
    overflow-y: auto;
}

.markdown-source code {
    background: none;
    padding: 0;
    color: inherit;
    font-size: inherit;
}

/* Markdown toggle button states */
.btn.markdown-active {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

/* Dark mode for markdown source */
@media (prefers-color-scheme: dark) {
    .markdown-source {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}

/* Voice Assistant Styles */
.voice-assistant {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    text-align: center;
}

.assistant-indicator {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.assistant-indicator:hover {
    transform: scale(1.1);
}

.assistant-indicator.listening {
    animation: pulse 2s infinite;
}

.pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0;
}

.assistant-indicator.listening .pulse-ring {
    animation: pulse-ring 2s infinite;
}

.assistant-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.assistant-status {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.voice-assistant:hover .assistant-status {
    opacity: 1;
}

/* Voice Animation */
.voice-animation {
    width: 100px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
}

.voice-wave {
    width: 4px;
    height: 20px;
    background: linear-gradient(to top, var(--primary-color), #0b5ed7);
    border-radius: 2px;
    animation: voice-wave 1.5s ease-in-out infinite;
}

.voice-wave:nth-child(2) { animation-delay: 0.1s; }
.voice-wave:nth-child(3) { animation-delay: 0.2s; }
.voice-wave:nth-child(4) { animation-delay: 0.3s; }
.voice-wave:nth-child(5) { animation-delay: 0.4s; }

/* Assistant Suggestions */
.assistant-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.suggestion-chip {
    background: var(--light-color);
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
}

.suggestion-chip:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Floating AI Chat Button */
.floating-chat-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 999;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.floating-chat-btn:hover {
    transform: scale(1.1);
}

.chat-btn-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    margin: 0 auto 10px;
    animation: chat-pulse 3s infinite;
}

.chat-btn-status {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 15px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.floating-chat-btn:hover .chat-btn-status {
    opacity: 1;
}

/* Enhanced Chat Interface */
.chat-quick-actions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.chat-controls .btn {
    padding: 0.25rem 0.5rem;
}

.suggestion-examples {
    font-size: 0.8rem;
    line-height: 1.3;
}

/* Floating Chat Modal */
.floating-chat-messages {
    background: #f8f9fa;
}

.floating-chat-messages .message {
    margin-bottom: 15px;
}

.floating-chat-messages .ai-message .message-content {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 15px 15px 15px 5px;
    padding: 12px 16px;
    max-width: 85%;
    display: inline-block;
}

.floating-chat-messages .user-message {
    text-align: right;
}

.floating-chat-messages .user-message .message-content {
    background: #17a2b8;
    color: white;
    border-radius: 15px 15px 5px 15px;
    padding: 12px 16px;
    max-width: 85%;
    display: inline-block;
}

.floating-chat-input textarea {
    border-radius: 20px;
    border: 2px solid #dee2e6;
    transition: border-color 0.3s ease;
}

.floating-chat-input textarea:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.floating-chat-input .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Chat Textarea */
#chat-input {
    border-radius: 15px;
    border: 2px solid #dee2e6;
    transition: border-color 0.3s ease;
    min-height: 38px;
    max-height: 120px;
}

#chat-input:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

/* Voice Assistant Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes pulse-ring {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 0;
    }
}

@keyframes voice-wave {
    0%, 100% { height: 20px; }
    50% { height: 40px; }
}

@keyframes chat-pulse {
    0%, 100% { box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3); }
    50% { box-shadow: 0 4px 25px rgba(23, 162, 184, 0.6); }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid;
    }

    .markdown-source {
        border: 2px solid #000;
    }

    .voice-assistant .assistant-icon {
        border: 2px solid #000;
    }

    .floating-chat-btn .chat-btn-icon {
        border: 2px solid #000;
    }
}

/* Responsive Design for Voice & Chat */
@media (max-width: 768px) {
    .voice-assistant {
        bottom: 80px;
        right: 15px;
    }

    .assistant-indicator {
        width: 50px;
        height: 50px;
    }

    .assistant-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .pulse-ring {
        width: 50px;
        height: 50px;
    }

    .floating-chat-btn {
        bottom: 140px;
        left: 15px;
    }

    .chat-btn-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .chat-quick-actions .btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Print styles - hide voice assistant and chat */
@media print {
    .voice-assistant,
    .floating-chat-btn {
        display: none !important;
    }
}
