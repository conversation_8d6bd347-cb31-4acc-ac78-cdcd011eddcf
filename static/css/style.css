/* Main CSS for Markdown CMS */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.content-area {
    min-height: calc(100vh - 200px);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
}

/* Chat Interface */
.chat-container {
    position: sticky;
    top: 20px;
}

.chat-messages {
    background-color: #f8f9fa;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.message {
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease-in;
}

.message-content {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 85%;
    word-wrap: break-word;
}

.user-message .message-content {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.ai-message .message-content {
    background-color: white;
    border: 1px solid #e9ecef;
    margin-right: auto;
}

.message-timestamp {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 5px;
}

.chat-input {
    background-color: white;
}

#chat-input {
    border: 1px solid #ced4da;
    border-radius: 20px;
    padding: 10px 15px;
}

#chat-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#send-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Content Styling */
.hero-section .jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-section .jumbotron .btn {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.hero-section .jumbotron .btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Post and Page Content */
.post-content, .page-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.post-title, .page-title {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.post-meta, .page-meta {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.post-body, .page-body {
    margin-top: 2rem;
}

.post-body h1, .post-body h2, .post-body h3, 
.page-body h1, .page-body h2, .page-body h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.post-body h1, .page-body h1 {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.post-body h2, .page-body h2 {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.3rem;
}

.post-body blockquote, .page-body blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.post-body code, .page-body code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #e83e8c;
}

.post-body pre, .page-body pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    border: 1px solid #e9ecef;
}

.post-body pre code, .page-body pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

.post-body img, .page-body img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

.post-body table, .page-body table {
    width: 100%;
    margin: 1.5rem 0;
    border-collapse: collapse;
}

.post-body table th, .post-body table td,
.page-body table th, .page-body table td {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    text-align: left;
}

.post-body table th, .page-body table th {
    background-color: var(--light-color);
    font-weight: 600;
}

/* Tags */
.tags .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* Sidebar */
.sidebar {
    background-color: #f8f9fa;
    min-height: calc(100vh - 56px);
}

.recent-posts .list-group-item {
    border: none;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.recent-posts .list-group-item:last-child {
    border-bottom: none;
}

/* Table of Contents */
.toc-container ul {
    list-style: none;
    padding-left: 0;
}

.toc-container ul ul {
    padding-left: 1rem;
}

.toc-container a {
    color: #6c757d;
    text-decoration: none;
    display: block;
    padding: 0.25rem 0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toc-container a:hover {
    color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
    padding-left: 0.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(13, 110, 253, 0.1);
    border-bottom: 1px solid rgba(13, 110, 253, 0.2);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0b5ed7);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0a58ca);
}

.btn-outline-purple {
    color: #6a1b9a;
    border-color: #6a1b9a;
}

.btn-outline-purple:hover {
    background-color: #6a1b9a;
    border-color: #6a1b9a;
    color: white;
}

/* Features Section */
.features-section .card {
    transition: transform 0.3s ease;
}

.features-section .card:hover {
    transform: translateY(-5px);
}

/* Footer */
footer {
    margin-top: auto;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-area {
        padding: 1rem !important;
    }
    
    .sidebar {
        padding: 1rem !important;
    }
    
    .post-title, .page-title {
        font-size: 2rem;
    }
    
    .hero-section .jumbotron {
        padding: 2rem !important;
    }
    
    .chat-messages {
        height: 250px;
    }
    
    .message-content {
        max-width: 95%;
    }
}

@media (max-width: 576px) {
    .post-title, .page-title {
        font-size: 1.5rem;
    }
    
    .post-meta, .page-meta {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .post-meta > div, .page-meta > div {
        margin-bottom: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .sidebar, .chat-container, .admin-actions, .share-buttons {
        display: none !important;
    }
    
    .content-area {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .post-content, .page-content {
        max-width: none !important;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Markdown Source View */
.markdown-source {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
    max-height: 70vh;
    overflow-y: auto;
}

.markdown-source code {
    background: none;
    padding: 0;
    color: inherit;
    font-size: inherit;
}

/* Markdown toggle button states */
.btn.markdown-active {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

/* Dark mode for markdown source */
@media (prefers-color-scheme: dark) {
    .markdown-source {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid;
    }

    .markdown-source {
        border: 2px solid #000;
    }
}
