/**
 * Voice Assistant Settings Management
 */

class VoiceAssistantSettings {
    constructor() {
        this.voices = [];
        this.currentSettings = this.loadSettings();
        this.diagnosticLog = document.getElementById('diagnostic-log');
        
        this.init();
    }
    
    init() {
        this.log('Initializing Voice Assistant Settings...');
        
        // Load voices
        this.loadVoices();
        
        // Check browser support
        this.checkBrowserSupport();
        
        // Check microphone permission
        this.checkMicrophonePermission();
        
        // Check voice synthesis
        this.checkVoiceSynthesis();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Load current settings into UI
        this.loadSettingsIntoUI();
        
        // Check assistant status
        this.checkAssistantStatus();
    }
    
    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = type === 'error' ? '[ERROR]' : type === 'warn' ? '[WARN]' : '[INFO]';
        const className = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : 'text-success';
        
        const logEntry = document.createElement('div');
        logEntry.className = className;
        logEntry.textContent = `${timestamp} ${prefix} ${message}`;
        
        this.diagnosticLog.appendChild(logEntry);
        this.diagnosticLog.scrollTop = this.diagnosticLog.scrollHeight;
        
        console.log(`${prefix} ${message}`);
    }
    
    loadSettings() {
        try {
            const settings = localStorage.getItem('voiceAssistantSettings');
            return settings ? JSON.parse(settings) : this.getDefaultSettings();
        } catch (error) {
            this.log('Failed to load settings, using defaults', 'warn');
            return this.getDefaultSettings();
        }
    }
    
    getDefaultSettings() {
        return {
            voice: {
                name: null,
                rate: 0.85,
                pitch: 1.1,
                volume: 0.9
            },
            recognition: {
                language: 'en-US',
                sensitivity: 0.7,
                continuous: true,
                interimResults: true
            },
            wakeWords: ['hey assistant', 'assistant', 'hey ai', 'ai assistant'],
            enabled: true
        };
    }
    
    saveSettings() {
        try {
            localStorage.setItem('voiceAssistantSettings', JSON.stringify(this.currentSettings));
            this.log('Settings saved successfully');
            return true;
        } catch (error) {
            this.log('Failed to save settings: ' + error.message, 'error');
            return false;
        }
    }
    
    loadVoices() {
        this.log('Loading available voices...');
        
        const loadVoicesCallback = () => {
            this.voices = speechSynthesis.getVoices();
            this.log(`Found ${this.voices.length} voices`);
            this.populateVoiceSelect();
        };
        
        // Load voices immediately if available
        if (speechSynthesis.getVoices().length > 0) {
            loadVoicesCallback();
        }
        
        // Also listen for the voiceschanged event
        speechSynthesis.addEventListener('voiceschanged', loadVoicesCallback);
    }
    
    populateVoiceSelect() {
        const voiceSelect = document.getElementById('voice-select');
        voiceSelect.innerHTML = '';
        
        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Auto-select best female voice';
        voiceSelect.appendChild(defaultOption);
        
        // Group voices by language
        const voicesByLang = {};
        this.voices.forEach(voice => {
            const lang = voice.lang.split('-')[0];
            if (!voicesByLang[lang]) {
                voicesByLang[lang] = [];
            }
            voicesByLang[lang].push(voice);
        });
        
        // Add voices to select
        Object.keys(voicesByLang).sort().forEach(lang => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = this.getLanguageName(lang);
            
            voicesByLang[lang].forEach(voice => {
                const option = document.createElement('option');
                option.value = voice.name;
                option.textContent = `${voice.name} (${voice.lang})`;
                optgroup.appendChild(option);
            });
            
            voiceSelect.appendChild(optgroup);
        });
        
        this.log(`Populated voice selector with ${this.voices.length} voices`);
    }
    
    getLanguageName(code) {
        const languages = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese'
        };
        return languages[code] || code.toUpperCase();
    }
    
    checkBrowserSupport() {
        const indicator = document.getElementById('browser-support-indicator');
        
        const speechRecognition = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
        const speechSynthesis = 'speechSynthesis' in window;
        const mediaDevices = 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
        
        if (speechRecognition && speechSynthesis && mediaDevices) {
            indicator.innerHTML = '<i class="fas fa-check text-success me-2"></i>Fully Supported';
            this.log('Browser fully supports voice assistant features');
        } else {
            indicator.innerHTML = '<i class="fas fa-exclamation-triangle text-warning me-2"></i>Limited Support';
            this.log('Browser has limited voice assistant support', 'warn');
        }
    }
    
    async checkMicrophonePermission() {
        const indicator = document.getElementById('microphone-indicator');
        
        try {
            if (navigator.permissions) {
                const permission = await navigator.permissions.query({ name: 'microphone' });
                
                if (permission.state === 'granted') {
                    indicator.innerHTML = '<i class="fas fa-check text-success me-2"></i>Granted';
                    this.log('Microphone permission granted');
                } else if (permission.state === 'denied') {
                    indicator.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Denied';
                    this.log('Microphone permission denied', 'error');
                } else {
                    indicator.innerHTML = '<i class="fas fa-question text-warning me-2"></i>Not Requested';
                    this.log('Microphone permission not yet requested', 'warn');
                }
            } else {
                indicator.innerHTML = '<i class="fas fa-question text-info me-2"></i>Unknown';
                this.log('Cannot check microphone permission', 'warn');
            }
        } catch (error) {
            indicator.innerHTML = '<i class="fas fa-exclamation-triangle text-warning me-2"></i>Error';
            this.log('Error checking microphone permission: ' + error.message, 'error');
        }
    }
    
    checkVoiceSynthesis() {
        const indicator = document.getElementById('voice-indicator');
        
        if ('speechSynthesis' in window) {
            const voiceCount = speechSynthesis.getVoices().length;
            indicator.innerHTML = `<i class="fas fa-check text-success me-2"></i>Available (${voiceCount} voices)`;
            this.log(`Voice synthesis available with ${voiceCount} voices`);
        } else {
            indicator.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Not Available';
            this.log('Voice synthesis not available', 'error');
        }
    }
    
    checkAssistantStatus() {
        const indicator = document.getElementById('assistant-indicator');
        
        if (window.voiceAssistant) {
            indicator.innerHTML = '<i class="fas fa-check text-success me-2"></i>Active';
            this.log('Voice assistant is active and ready');
        } else {
            indicator.innerHTML = '<i class="fas fa-exclamation-triangle text-warning me-2"></i>Not Initialized';
            this.log('Voice assistant not initialized', 'warn');
        }
    }
    
    loadSettingsIntoUI() {
        // Voice settings
        document.getElementById('voice-select').value = this.currentSettings.voice.name || '';
        document.getElementById('voice-rate').value = this.currentSettings.voice.rate;
        document.getElementById('voice-pitch').value = this.currentSettings.voice.pitch;
        document.getElementById('voice-volume').value = this.currentSettings.voice.volume;
        
        // Update display values
        document.getElementById('rate-value').textContent = this.currentSettings.voice.rate;
        document.getElementById('pitch-value').textContent = this.currentSettings.voice.pitch;
        document.getElementById('volume-value').textContent = this.currentSettings.voice.volume;
        
        // Recognition settings
        document.getElementById('language-select').value = this.currentSettings.recognition.language;
        document.getElementById('sensitivity').value = this.currentSettings.recognition.sensitivity;
        document.getElementById('continuous-listening').checked = this.currentSettings.recognition.continuous;
        document.getElementById('interim-results').checked = this.currentSettings.recognition.interimResults;
        
        // Update sensitivity display
        document.getElementById('sensitivity-value').textContent = this.currentSettings.recognition.sensitivity;
        
        // Wake words
        this.updateWakeWordsList();
        
        this.log('Settings loaded into UI');
    }
    
    updateWakeWordsList() {
        const container = document.getElementById('wake-words-list');
        container.innerHTML = '';
        
        this.currentSettings.wakeWords.forEach((word, index) => {
            const wordElement = document.createElement('div');
            wordElement.className = 'wake-word-item d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded';
            wordElement.innerHTML = `
                <span class="wake-word-text">"${word}"</span>
                <button type="button" class="btn btn-sm btn-outline-danger remove-wake-word" data-index="${index}">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(wordElement);
        });
        
        // Add event listeners for remove buttons
        container.querySelectorAll('.remove-wake-word').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.closest('button').dataset.index);
                this.removeWakeWord(index);
            });
        });
    }
    
    addWakeWord() {
        const input = document.getElementById('new-wake-word');
        const newWord = input.value.trim().toLowerCase();
        
        if (newWord && !this.currentSettings.wakeWords.includes(newWord)) {
            this.currentSettings.wakeWords.push(newWord);
            this.updateWakeWordsList();
            input.value = '';
            this.log(`Added wake word: "${newWord}"`);
        } else if (this.currentSettings.wakeWords.includes(newWord)) {
            this.log(`Wake word "${newWord}" already exists`, 'warn');
        }
    }
    
    removeWakeWord(index) {
        const removedWord = this.currentSettings.wakeWords[index];
        this.currentSettings.wakeWords.splice(index, 1);
        this.updateWakeWordsList();
        this.log(`Removed wake word: "${removedWord}"`);
    }
    
    setupEventListeners() {
        // Range inputs for real-time updates
        ['voice-rate', 'voice-pitch', 'voice-volume', 'sensitivity'].forEach(id => {
            const element = document.getElementById(id);
            element.addEventListener('input', (e) => {
                const valueSpan = document.getElementById(id.replace('voice-', '').replace('sensitivity', 'sensitivity') + '-value');
                if (valueSpan) {
                    valueSpan.textContent = e.target.value;
                }
            });
        });
        
        // Save buttons
        document.getElementById('save-voice-settings').addEventListener('click', () => this.saveVoiceSettings());
        document.getElementById('save-recognition-settings').addEventListener('click', () => this.saveRecognitionSettings());
        
        // Test buttons
        document.getElementById('test-voice').addEventListener('click', () => this.testVoice());
        document.getElementById('test-voice-assistant').addEventListener('click', () => this.testVoiceAssistant());
        document.getElementById('test-wake-words').addEventListener('click', () => this.testWakeWords());
        
        // Wake word management
        document.getElementById('add-wake-word').addEventListener('click', () => this.addWakeWord());
        document.getElementById('new-wake-word').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addWakeWord();
            }
        });
        
        // Troubleshooting buttons
        document.getElementById('reset-permissions').addEventListener('click', () => this.resetPermissions());
        document.getElementById('clear-cache').addEventListener('click', () => this.clearCache());
        document.getElementById('run-diagnostics').addEventListener('click', () => this.runDiagnostics());
    }
    
    saveVoiceSettings() {
        this.currentSettings.voice = {
            name: document.getElementById('voice-select').value || null,
            rate: parseFloat(document.getElementById('voice-rate').value),
            pitch: parseFloat(document.getElementById('voice-pitch').value),
            volume: parseFloat(document.getElementById('voice-volume').value)
        };
        
        if (this.saveSettings()) {
            this.showNotification('Voice settings saved successfully!', 'success');
            
            // Update voice assistant if available
            if (window.voiceAssistant) {
                window.voiceAssistant.updateVoiceSettings(this.currentSettings.voice);
                this.log('Updated voice assistant with new settings');
            }
        }
    }
    
    saveRecognitionSettings() {
        this.currentSettings.recognition = {
            language: document.getElementById('language-select').value,
            sensitivity: parseFloat(document.getElementById('sensitivity').value),
            continuous: document.getElementById('continuous-listening').checked,
            interimResults: document.getElementById('interim-results').checked
        };
        
        if (this.saveSettings()) {
            this.showNotification('Recognition settings saved successfully!', 'success');
            
            // Update voice assistant if available
            if (window.voiceAssistant) {
                window.voiceAssistant.updateRecognitionSettings(this.currentSettings.recognition);
                this.log('Updated voice assistant with new recognition settings');
            }
        }
    }
    
    testVoice() {
        const settings = {
            name: document.getElementById('voice-select').value || null,
            rate: parseFloat(document.getElementById('voice-rate').value),
            pitch: parseFloat(document.getElementById('voice-pitch').value),
            volume: parseFloat(document.getElementById('voice-volume').value)
        };
        
        const utterance = new SpeechSynthesisUtterance(
            'Hello! This is a test of your voice assistant settings. How do I sound?'
        );
        
        if (settings.name) {
            const voice = this.voices.find(v => v.name === settings.name);
            if (voice) {
                utterance.voice = voice;
            }
        } else {
            // Auto-select best female voice
            const femaleVoice = this.voices.find(voice => 
                voice.name.includes('Samantha') || 
                voice.name.includes('Victoria') || 
                voice.name.includes('Zira') || 
                voice.name.includes('Hazel') ||
                (voice.lang.startsWith('en') && voice.name.toLowerCase().includes('female'))
            );
            if (femaleVoice) {
                utterance.voice = femaleVoice;
            }
        }
        
        utterance.rate = settings.rate;
        utterance.pitch = settings.pitch;
        utterance.volume = settings.volume;
        
        utterance.onstart = () => this.log('Voice test started');
        utterance.onend = () => this.log('Voice test completed');
        utterance.onerror = (e) => this.log('Voice test error: ' + e.error, 'error');
        
        speechSynthesis.speak(utterance);
    }
    
    testVoiceAssistant() {
        if (window.voiceAssistant) {
            this.log('Testing voice assistant wake up...');
            window.voiceAssistant.wakeUp();
        } else {
            this.log('Voice assistant not available for testing', 'error');
            this.showNotification('Voice assistant not initialized', 'danger');
        }
    }
    
    testWakeWords() {
        this.log('Starting wake word test - say one of your wake words...');
        this.showNotification('Say one of your wake words now...', 'info');
        
        // This would integrate with the voice assistant's wake word detection
        if (window.voiceAssistant) {
            window.voiceAssistant.startListening();
        }
    }
    
    resetPermissions() {
        this.log('Resetting permissions...');
        localStorage.removeItem('voiceAssistantState');
        this.showNotification('Permissions reset. Please refresh the page.', 'warning');
    }
    
    clearCache() {
        this.log('Clearing voice assistant cache...');
        localStorage.removeItem('voiceAssistantSettings');
        localStorage.removeItem('voiceAssistantState');
        this.showNotification('Cache cleared. Please refresh the page.', 'warning');
    }
    
    runDiagnostics() {
        this.log('Running comprehensive diagnostics...');
        
        // Re-run all checks
        this.checkBrowserSupport();
        this.checkMicrophonePermission();
        this.checkVoiceSynthesis();
        this.checkAssistantStatus();
        
        this.log('Diagnostics completed');
        this.showNotification('Diagnostics completed. Check the log for details.', 'info');
    }
    
    showNotification(message, type = 'info') {
        // Create Bootstrap toast notification
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
    
    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1100';
        document.body.appendChild(container);
        return container;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.voiceAssistantSettings = new VoiceAssistantSettings();
});
