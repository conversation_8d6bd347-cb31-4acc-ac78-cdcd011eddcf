/**
 * Voice Assistant - Alexa-like functionality for the CMS
 * Supports wake word detection and voice commands
 */

class VoiceAssistant {
    constructor() {
        console.log('VoiceAssistant constructor called');

        this.isListening = false;
        this.isAwake = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.currentUtterance = null;
        this.wakeWords = ['hey assistant', 'assistant', 'hey ai', 'ai assistant'];
        this.commands = this.initializeCommands();
        this.femaleVoice = null;

        console.log('VoiceAssistant properties initialized, calling init()');
        this.init();
    }

    init() {
        console.log('VoiceAssistant init() called');

        try {
            console.log('Setting up speech recognition...');
            this.setupSpeechRecognition();

            console.log('Setting up voice selection...');
            this.setupVoiceSelection();

            console.log('Binding events...');
            this.bindEvents();

            console.log('Requesting microphone permission...');
            this.requestMicrophonePermission();

            console.log('VoiceAssistant initialization complete');
        } catch (error) {
            console.error('Error in VoiceAssistant init():', error);
        }
    }

    setupSpeechRecognition() {
        // Check browser and version
        const userAgent = navigator.userAgent;
        console.log('Browser User Agent:', userAgent);
        console.log('Is Edge:', userAgent.includes('Edg'));
        console.log('Is Chrome:', userAgent.includes('Chrome'));
        console.log('Is Safari:', userAgent.includes('Safari'));

        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('Speech recognition not supported in this browser');
            this.updateStatus('Voice not supported in this browser');
            return;
        }

        console.log('Speech recognition is supported');
        console.log('webkitSpeechRecognition available:', 'webkitSpeechRecognition' in window);
        console.log('SpeechRecognition available:', 'SpeechRecognition' in window);

        // Edge/Chrome use different constructors
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        console.log('Using SpeechRecognition constructor:', SpeechRecognition.name);

        try {
            this.recognition = new SpeechRecognition();
            console.log('Speech recognition object created successfully');
        } catch (error) {
            console.error('Failed to create speech recognition object:', error);
            this.updateStatus('Failed to initialize speech recognition');
            return;
        }

        // Configure recognition settings
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = 'en-US';

        // Edge-specific settings
        if (userAgent.includes('Edg')) {
            console.log('Applying Edge-specific settings');
            this.recognition.maxAlternatives = 1;
            this.recognition.serviceURI = undefined; // Let Edge use default
        }

        this.recognition.onstart = () => {
            console.log('Speech recognition started');
            this.isListening = true;
            this.updateStatus('Listening - Say "Hey Assistant"');
            this.updateIndicator();
        };

        this.recognition.onend = () => {
            console.log('Speech recognition ended');
            this.isListening = false;
            this.updateIndicator();

            // Restart listening if not manually stopped
            if (!this.isAwake) {
                setTimeout(() => this.startListening(), 1000);
            }
        };

        this.recognition.onresult = (event) => {
            this.handleSpeechResult(event);
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            if (event.error === 'not-allowed') {
                this.showPermissionError();
                this.showPermissionToast();
            } else if (event.error === 'no-speech') {
                console.log('No speech detected, continuing to listen...');
            } else {
                console.log('Speech recognition error, will retry...');
            }
        };
    }

    setupVoiceSelection() {
        // Wait for voices to load
        if (this.synthesis.getVoices().length === 0) {
            this.synthesis.addEventListener('voiceschanged', () => {
                this.selectFemaleVoice();
            });
        } else {
            this.selectFemaleVoice();
        }
    }

    selectFemaleVoice() {
        const voices = this.synthesis.getVoices();
        
        // Priority order for soft, pleasant female voices
        const preferredVoices = [
            'Samantha', 'Victoria', 'Allison', 'Ava', 'Susan', 'Joanna', 'Salli', 'Kimberly',
            'Microsoft Zira', 'Microsoft Hazel', 'Google UK English Female', 'Google US English',
            'Alex', 'Karen', 'Moira', 'Tessa', 'Veena', 'Fiona'
        ];

        // Find the best female voice
        for (const preferredName of preferredVoices) {
            this.femaleVoice = voices.find(voice => 
                voice.name.includes(preferredName) && 
                voice.lang.startsWith('en')
            );
            if (this.femaleVoice) break;
        }

        // Fallback to any English female voice
        if (!this.femaleVoice) {
            this.femaleVoice = voices.find(voice => 
                voice.lang.startsWith('en') && 
                (voice.name.toLowerCase().includes('female') || 
                 voice.name.toLowerCase().includes('woman') ||
                 voice.name.toLowerCase().includes('zira') ||
                 voice.name.toLowerCase().includes('hazel'))
            );
        }

        // Final fallback to any English voice
        if (!this.femaleVoice) {
            this.femaleVoice = voices.find(voice => voice.lang.startsWith('en'));
        }

        console.log('Selected voice:', this.femaleVoice?.name || 'Default');
    }

    initializeCommands() {
        return {
            // Content commands
            'read this post': () => this.readCurrentPost(),
            'read this page': () => this.readCurrentPost(),
            'read aloud': () => this.readCurrentPost(),
            'stop reading': () => this.stopReading(),

            // Navigation commands
            'show recent posts': () => this.showRecentPosts(),
            'show me recent posts': () => this.showRecentPosts(),
            'go home': () => this.goHome(),
            'go to home': () => this.goHome(),

            // Admin commands
            'open admin': () => this.openAdmin(),
            'open admin panel': () => this.openAdmin(),
            'go to admin': () => this.openAdmin(),
            'go to dashboard': () => this.goToDashboard(),
            'open dashboard': () => this.goToDashboard(),
            'show dashboard': () => this.goToDashboard(),

            // Admin content management
            'show all posts': () => this.showAllPosts(),
            'show me all posts': () => this.showAllPosts(),
            'manage posts': () => this.showAllPosts(),
            'show all pages': () => this.showAllPages(),
            'show me all pages': () => this.showAllPages(),
            'manage pages': () => this.showAllPages(),

            // Content creation
            'create a new post': () => this.createNewPost(),
            'create new post': () => this.createNewPost(),
            'new post': () => this.createNewPost(),
            'create a new page': () => this.createNewPage(),
            'create new page': () => this.createNewPage(),
            'new page': () => this.createNewPage(),

            // Settings
            'open ai settings': () => this.openAISettings(),
            'ai settings': () => this.openAISettings(),
            'open settings': () => this.openAISettings(),
            'open api keys': () => this.openAPIKeys(),
            'api keys': () => this.openAPIKeys(),
            'web search settings': () => this.openAPIKeys(),

            // Search commands
            'search for': (query) => this.performSearch(query),
            'find': (query) => this.performSearch(query),
            'look for': (query) => this.performSearch(query),

            // Information commands
            'what time is it': () => this.tellTime(),
            'what is the time': () => this.tellTime(),
            'help': () => this.showHelp(),
            'what can you do': () => this.showHelp(),

            // Control commands
            'cancel': () => this.cancelCommand(),
            'never mind': () => this.cancelCommand(),
            'goodbye': () => this.sayGoodbye(),
            'bye': () => this.sayGoodbye(),
            'thank you': () => this.sayYoureWelcome(),
            'thanks': () => this.sayYoureWelcome()
        };
    }

    async requestMicrophonePermission() {
        console.log('Requesting microphone permission...');
        const userAgent = navigator.userAgent;

        // Check for saved state first
        const savedState = this.loadState();
        if (savedState && savedState.permissionGranted) {
            console.log('Found saved permission state, attempting to restore...');
        }

        try {
            // Check if we already have permission (may not work in all browsers)
            if (navigator.permissions && navigator.permissions.query) {
                try {
                    const permission = await navigator.permissions.query({ name: 'microphone' });
                    console.log('Current permission state:', permission.state);

                    if (permission.state === 'granted') {
                        console.log('Microphone permission already granted');
                        this.onPermissionGranted();
                        return;
                    }
                } catch (permError) {
                    console.log('Permission query not supported, proceeding with direct request');
                }
            }

            // Request microphone permission
            console.log('Requesting microphone access...');
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            console.log('Microphone stream obtained, stopping tracks...');
            stream.getTracks().forEach(track => track.stop()); // Stop the stream immediately

            console.log('Microphone permission granted');
            this.onPermissionGranted();

        } catch (error) {
            console.error('Microphone permission error:', error);
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);

            // Edge-specific error handling
            if (userAgent.includes('Edg')) {
                console.log('Handling Edge-specific permission error');
                if (error.name === 'NotAllowedError') {
                    this.updateStatus('Click to allow microphone access');
                } else if (error.name === 'NotFoundError') {
                    this.updateStatus('No microphone found');
                } else {
                    this.updateStatus('Microphone error - try refreshing');
                }
            }

            this.showPermissionError();
            this.showPermissionToast();
        }
    }

    onPermissionGranted() {
        this.updateStatus('Microphone ready - Say "Hey Assistant"');
        this.updateIndicator();
        this.startListening();

        // Save permission state
        this.saveState();

        // Hide permission toast if it's showing
        const toastElement = document.getElementById('micPermissionToast');
        if (toastElement) {
            const toast = bootstrap.Toast.getInstance(toastElement);
            if (toast) {
                toast.hide();
            }
        }
    }

    saveState() {
        try {
            const state = {
                permissionGranted: true,
                timestamp: Date.now(),
                voiceName: this.femaleVoice?.name || null,
                settings: {
                    rate: 0.85,
                    pitch: 1.1,
                    volume: 0.9
                }
            };
            localStorage.setItem('voiceAssistantState', JSON.stringify(state));
            console.log('Voice assistant state saved');
        } catch (error) {
            console.log('Could not save voice assistant state:', error);
        }
    }

    loadState() {
        try {
            const stateStr = localStorage.getItem('voiceAssistantState');
            if (stateStr) {
                const state = JSON.parse(stateStr);

                // Check if state is recent (within 24 hours)
                const hoursSinceLastUse = (Date.now() - state.timestamp) / (1000 * 60 * 60);
                if (hoursSinceLastUse < 24 && state.permissionGranted) {
                    console.log('Restoring voice assistant state from previous session');
                    return state;
                }
            }
        } catch (error) {
            console.log('Could not load voice assistant state:', error);
        }
        return null;
    }

    bindEvents() {
        const indicator = document.getElementById('assistant-indicator');
        if (indicator) {
            indicator.addEventListener('click', () => {
                if (this.isAwake) {
                    this.stopVoiceAssistant();
                } else if (!this.isListening) {
                    // Try to start listening if not already
                    this.requestMicrophonePermission();
                } else {
                    this.wakeUp();
                }
            });
        }
    }

    startListening() {
        if (!this.recognition) {
            console.log('Speech recognition not available');
            return;
        }

        if (this.isListening) {
            console.log('Already listening');
            return;
        }

        const userAgent = navigator.userAgent;

        try {
            console.log('Starting speech recognition...');

            // Edge-specific pre-start checks
            if (userAgent.includes('Edg')) {
                console.log('Edge detected - applying compatibility settings');

                // Ensure recognition is properly configured for Edge
                this.recognition.continuous = true;
                this.recognition.interimResults = true;
                this.recognition.lang = 'en-US';

                // Add extra delay for Edge
                setTimeout(() => {
                    try {
                        this.recognition.start();
                    } catch (edgeError) {
                        console.error('Edge-specific start error:', edgeError);
                        this.handleStartError(edgeError);
                    }
                }, 100);
            } else {
                this.recognition.start();
            }

        } catch (error) {
            console.error('Failed to start speech recognition:', error);
            this.handleStartError(error);
        }
    }

    handleStartError(error) {
        console.error('Start error details:', {
            name: error.name,
            message: error.message,
            code: error.code
        });

        if (error.name === 'InvalidStateError') {
            console.log('Recognition already running, stopping and restarting...');
            this.recognition.stop();
            setTimeout(() => this.startListening(), 1000);
        } else if (error.name === 'NotAllowedError') {
            console.log('Permission not allowed');
            this.showPermissionError();
            this.showPermissionToast();
        } else if (error.name === 'ServiceNotAllowedError') {
            console.log('Speech service not allowed');
            this.updateStatus('Speech service blocked - check browser settings');
        } else {
            console.log('Unknown error, showing permission toast');
            this.updateStatus('Voice assistant error - click to retry');
            this.showPermissionToast();
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    handleSpeechResult(event) {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript.toLowerCase().trim();
            
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }

        const fullTranscript = (finalTranscript + interimTranscript).toLowerCase();

        if (!this.isAwake) {
            // Check for wake words
            if (this.wakeWords.some(wake => fullTranscript.includes(wake))) {
                this.wakeUp();
                return;
            }
        } else {
            // Process commands when awake
            if (finalTranscript) {
                this.processCommand(finalTranscript);
            }
        }
    }

    wakeUp() {
        this.isAwake = true;
        this.stopListening();
        this.updateIndicator();
        this.showModal();

        // Context-aware greeting
        const currentPath = window.location.pathname;
        let greeting = "Hello! How can I help you today?";

        if (currentPath.startsWith('/admin')) {
            if (currentPath.includes('/posts')) {
                greeting = "Hello! I can help you manage your posts. What would you like to do?";
            } else if (currentPath.includes('/pages')) {
                greeting = "Hello! I can help you manage your pages. What would you like to do?";
            } else if (currentPath.includes('/ai-settings')) {
                greeting = "Hello! I can help you with AI settings or navigate elsewhere. What would you like to do?";
            } else {
                greeting = "Hello! I can help you manage your CMS. What would you like to do?";
            }
        } else if (currentPath.startsWith('/post/')) {
            greeting = "Hello! I can read this post aloud or help you navigate. What would you like to do?";
        } else if (currentPath.startsWith('/page/')) {
            greeting = "Hello! I can read this page aloud or help you navigate. What would you like to do?";
        }

        this.speak(greeting);

        // Start listening for commands
        setTimeout(() => {
            this.startListening();
        }, 3000);
    }

    processCommand(transcript) {
        console.log('Processing command:', transcript);
        
        // Find matching command
        for (const [command, action] of Object.entries(this.commands)) {
            if (transcript.includes(command)) {
                // Extract additional parameters for search commands
                if (command === 'search for' || command === 'find' || command === 'look for') {
                    const query = transcript.replace(command, '').trim();
                    if (query) {
                        action(query);
                    } else {
                        this.speak("What would you like me to search for?");
                    }
                } else {
                    action();
                }
                return;
            }
        }

        // No command found
        this.speak("I'm sorry, I didn't understand that command. Try saying 'help' to see what I can do.");
    }

    speak(text, callback = null) {
        // Stop any current speech
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        
        // Configure for soft, sexy female voice
        if (this.femaleVoice) {
            utterance.voice = this.femaleVoice;
        }
        
        utterance.rate = 0.9;    // Slightly slower for more pleasant delivery
        utterance.pitch = 1.1;   // Higher pitch for feminine voice
        utterance.volume = 0.9;  // Soft volume

        utterance.onend = () => {
            if (callback) callback();
        };

        this.currentUtterance = utterance;
        this.synthesis.speak(utterance);
    }

    updateStatus(message) {
        const status = document.getElementById('assistant-status');
        if (status) {
            status.textContent = message;
            status.style.color = ''; // Reset color
        }
    }

    updateIndicator() {
        const indicator = document.getElementById('assistant-indicator');
        const status = document.getElementById('assistant-status');
        const icon = document.getElementById('assistant-icon');

        if (!indicator || !status || !icon) return;

        if (this.isAwake) {
            indicator.classList.add('listening');
            status.textContent = 'Listening for commands...';
            icon.className = 'fas fa-microphone-alt';
        } else if (this.isListening) {
            indicator.classList.add('listening');
            status.textContent = 'Say "Hey Assistant" to wake me up';
            icon.className = 'fas fa-microphone';
        } else {
            indicator.classList.remove('listening');
            status.textContent = 'Click to activate voice assistant';
            icon.className = 'fas fa-microphone-slash';
        }
    }

    showModal() {
        // Update suggestions based on current page
        this.updateContextualSuggestions();

        const modal = new bootstrap.Modal(document.getElementById('voiceAssistantModal'));
        modal.show();
    }

    updateContextualSuggestions() {
        const suggestionsContainer = document.getElementById('assistant-suggestions');
        if (!suggestionsContainer) return;

        const currentPath = window.location.pathname;
        let suggestions = [];

        if (currentPath.startsWith('/admin')) {
            if (currentPath.includes('/posts')) {
                suggestions = [
                    { text: '"Create a new post"', command: 'create a new post' },
                    { text: '"Go to dashboard"', command: 'go to dashboard' },
                    { text: '"Show all pages"', command: 'show all pages' },
                    { text: '"Open AI settings"', command: 'open ai settings' }
                ];
            } else if (currentPath.includes('/pages')) {
                suggestions = [
                    { text: '"Create a new page"', command: 'create a new page' },
                    { text: '"Show all posts"', command: 'show all posts' },
                    { text: '"Go to dashboard"', command: 'go to dashboard' },
                    { text: '"Open API keys"', command: 'open api keys' }
                ];
            } else if (currentPath.includes('/ai-settings')) {
                suggestions = [
                    { text: '"Create a new post"', command: 'create a new post' },
                    { text: '"Show all posts"', command: 'show all posts' },
                    { text: '"Open API keys"', command: 'open api keys' },
                    { text: '"Go to dashboard"', command: 'go to dashboard' }
                ];
            } else {
                // Default admin suggestions
                suggestions = [
                    { text: '"Create a new post"', command: 'create a new post' },
                    { text: '"Show all posts"', command: 'show all posts' },
                    { text: '"Show all pages"', command: 'show all pages' },
                    { text: '"Open AI settings"', command: 'open ai settings' }
                ];
            }
        } else {
            // Public site suggestions
            if (currentPath.startsWith('/post/')) {
                suggestions = [
                    { text: '"Read this post"', command: 'read this post' },
                    { text: '"Show recent posts"', command: 'show recent posts' },
                    { text: '"Open admin"', command: 'open admin' },
                    { text: '"Search for something"', command: 'search for something' }
                ];
            } else if (currentPath.startsWith('/page/')) {
                suggestions = [
                    { text: '"Read this page"', command: 'read this page' },
                    { text: '"Go home"', command: 'go home' },
                    { text: '"Open admin"', command: 'open admin' },
                    { text: '"Search for something"', command: 'search for something' }
                ];
            } else {
                // Default public suggestions
                suggestions = [
                    { text: '"Show recent posts"', command: 'show recent posts' },
                    { text: '"Open admin"', command: 'open admin' },
                    { text: '"Search for something"', command: 'search for something' },
                    { text: '"What time is it?"', command: 'what time is it' }
                ];
            }
        }

        // Update the suggestions HTML
        suggestionsContainer.innerHTML = suggestions.map(suggestion =>
            `<div class="suggestion-chip" onclick="executeVoiceCommand('${suggestion.command}')">
                ${suggestion.text}
            </div>`
        ).join('');
    }

    hideModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('voiceAssistantModal'));
        if (modal) {
            modal.hide();
        }
    }

    // Command implementations
    readCurrentPost() {
        const readAloudBtn = document.getElementById('read-aloud-btn') || document.getElementById('sidebar-read-aloud-btn');
        if (readAloudBtn) {
            this.speak("Starting to read the content for you.");
            setTimeout(() => {
                readAloudBtn.click();
                this.stopVoiceAssistant();
            }, 1500);
        } else {
            this.speak("I'm sorry, I can't find any content to read on this page.");
        }
    }

    stopReading() {
        // Stop text-to-speech if active
        if (window.speechSynthesis) {
            window.speechSynthesis.cancel();
        }

        // Stop read aloud if active
        if (typeof stopReading === 'function') {
            stopReading();
        }

        this.speak("I've stopped reading.");
        this.stopVoiceAssistant();
    }

    showRecentPosts() {
        this.speak("Taking you to the recent posts.");
        setTimeout(() => {
            window.location.href = '/';
            this.stopVoiceAssistant();
        }, 1500);
    }

    openAdmin() {
        this.speak("Opening the admin panel for you.");
        setTimeout(() => {
            window.location.href = '/admin';
            this.stopVoiceAssistant();
        }, 1500);
    }

    goToDashboard() {
        this.speak("Taking you to the dashboard.");
        setTimeout(() => {
            window.location.href = '/admin';
            this.stopVoiceAssistant();
        }, 1500);
    }

    showAllPosts() {
        this.speak("Showing all posts in the admin panel.");
        setTimeout(() => {
            window.location.href = '/admin/posts';
            this.stopVoiceAssistant();
        }, 1500);
    }

    showAllPages() {
        this.speak("Showing all pages in the admin panel.");
        setTimeout(() => {
            window.location.href = '/admin/pages';
            this.stopVoiceAssistant();
        }, 1500);
    }

    createNewPost() {
        this.speak("Creating a new post for you.");
        setTimeout(() => {
            window.location.href = '/admin/posts/new';
            this.stopVoiceAssistant();
        }, 1500);
    }

    createNewPage() {
        this.speak("Creating a new page for you.");
        setTimeout(() => {
            window.location.href = '/admin/pages/new';
            this.stopVoiceAssistant();
        }, 1500);
    }

    openAISettings() {
        this.speak("Opening AI settings.");
        setTimeout(() => {
            window.location.href = '/admin/ai-settings';
            this.stopVoiceAssistant();
        }, 1500);
    }

    openAPIKeys() {
        this.speak("Opening API keys settings.");
        setTimeout(() => {
            window.location.href = '/admin/web-search-settings';
            this.stopVoiceAssistant();
        }, 1500);
    }

    performSearch(query) {
        this.speak(`Searching for ${query}.`);
        setTimeout(() => {
            // Use the chat to search
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.value = `Search for: ${query}`;
                chatInput.focus();

                // Trigger search
                const sendBtn = document.querySelector('.chat-send-btn');
                if (sendBtn) {
                    sendBtn.click();
                }

                // Scroll to chat
                document.querySelector('.chat-container')?.scrollIntoView({
                    behavior: 'smooth'
                });
            }
            this.stopVoiceAssistant();
        }, 1500);
    }

    goHome() {
        this.speak("Taking you to the home page.");
        setTimeout(() => {
            window.location.href = '/';
            this.stopVoiceAssistant();
        }, 1500);
    }

    tellTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        this.speak(`The current time is ${timeString}.`);
        setTimeout(() => this.stopVoiceAssistant(), 3000);
    }

    showHelp() {
        // Detect if we're in admin area
        const isAdmin = window.location.pathname.startsWith('/admin');

        let helpText;
        if (isAdmin) {
            helpText = `I can help you manage your CMS! You can say:
            "Create a new post" or "Create a new page" to start writing,
            "Show all posts" or "Show all pages" to manage content,
            "Go to dashboard" to see the overview,
            "Open AI settings" or "Open API keys" for configuration,
            "Go home" to return to the main site,
            "What time is it" for the current time,
            or "Search for" followed by your query.
            Just say "Hey Assistant" anytime to wake me up!`;
        } else {
            helpText = `I can help you navigate and interact with this site! You can say:
            "Read this post" to have me read content aloud,
            "Show recent posts" to go to the home page,
            "Open admin" to access the admin panel,
            "Search for" followed by your query to search the site,
            "What time is it" to get the current time,
            or "Go home" to return to the main page.
            Just say "Hey Assistant" anytime to wake me up!`;
        }

        this.speak(helpText);
        setTimeout(() => this.stopVoiceAssistant(), 20000);
    }

    cancelCommand() {
        this.speak("Okay, canceling that.");
        this.stopVoiceAssistant();
    }

    sayGoodbye() {
        this.speak("Goodbye! Say 'Hey Assistant' anytime you need me.");
        this.stopVoiceAssistant();
    }

    sayYoureWelcome() {
        this.speak("You're very welcome! Happy to help.");
        setTimeout(() => this.stopVoiceAssistant(), 2000);
    }

    stopVoiceAssistant() {
        this.isAwake = false;
        this.stopListening();
        this.hideModal();
        this.updateIndicator();

        // Restart passive listening after a delay
        setTimeout(() => {
            this.startListening();
        }, 2000);
    }

    showPermissionError() {
        const status = document.getElementById('assistant-status');
        const indicator = document.getElementById('assistant-indicator');
        const icon = document.getElementById('assistant-icon');

        if (status) {
            status.textContent = 'Click to enable microphone';
            status.style.color = '#dc3545';
        }

        if (indicator) {
            indicator.classList.remove('listening');
        }

        if (icon) {
            icon.className = 'fas fa-microphone-slash';
        }

        // Show a more helpful message
        console.log('To enable voice assistant:');
        console.log('1. Click the microphone icon in your browser address bar');
        console.log('2. Allow microphone access for this site');
        console.log('3. Refresh the page');
    }

    showPermissionToast() {
        const toastElement = document.getElementById('micPermissionToast');
        if (toastElement) {
            const toast = new bootstrap.Toast(toastElement, {
                autohide: false // Don't auto-hide, let user dismiss
            });
            toast.show();
        }
    }

    hideAssistant() {
        const assistant = document.getElementById('voice-assistant');
        if (assistant) {
            assistant.style.display = 'none';
        }
    }
}

// Global functions for template access
let voiceAssistant = null;

function initializeVoiceAssistant() {
    console.log('=== Initializing Voice Assistant ===');

    // Check if elements exist
    const indicator = document.getElementById('assistant-indicator');
    const status = document.getElementById('assistant-status');
    const icon = document.getElementById('assistant-icon');

    console.log('Voice assistant elements found:', {
        indicator: !!indicator,
        status: !!status,
        icon: !!icon
    });

    if (!indicator || !status || !icon) {
        console.error('Voice assistant elements not found in DOM');
        return;
    }

    // Check browser support
    const speechSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    console.log('Speech recognition supported:', speechSupported);

    if (!speechSupported) {
        status.textContent = 'Voice not supported in this browser';
        icon.className = 'fas fa-microphone-slash';
        console.warn('Speech recognition not supported');
        return;
    }

    try {
        voiceAssistant = new VoiceAssistant();
        window.voiceAssistant = voiceAssistant; // Make globally accessible
        console.log('Voice assistant initialized successfully');
    } catch (error) {
        console.error('Error initializing voice assistant:', error);
        status.textContent = 'Voice assistant failed to load';
        icon.className = 'fas fa-exclamation-triangle';
    }
}

function stopVoiceAssistant() {
    if (voiceAssistant) {
        voiceAssistant.stopVoiceAssistant();
    }
}

function executeVoiceCommand(command) {
    if (voiceAssistant) {
        voiceAssistant.processCommand(command);
    }
}

// Debug function - call this in browser console to check status
function debugVoiceAssistant() {
    console.log('=== Voice Assistant Debug Info ===');
    console.log('Voice Assistant exists:', !!window.voiceAssistant);
    console.log('Speech Recognition supported:', 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window);
    console.log('Is listening:', window.voiceAssistant?.isListening);
    console.log('Is awake:', window.voiceAssistant?.isAwake);

    if (window.voiceAssistant) {
        console.log('Recognition object:', !!window.voiceAssistant.recognition);

        // Check microphone permission
        navigator.permissions.query({ name: 'microphone' }).then(permission => {
            console.log('Microphone permission:', permission.state);
        }).catch(err => {
            console.log('Could not check microphone permission:', err);
        });
    }

    console.log('=== End Debug Info ===');
}
