/**
 * Voice Assistant - Alexa-like functionality for the CMS
 * Supports wake word detection and voice commands
 */

class VoiceAssistant {
    constructor() {
        this.isListening = false;
        this.isAwake = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.currentUtterance = null;
        this.wakeWords = ['hey assistant', 'assistant', 'hey ai', 'ai assistant'];
        this.commands = this.initializeCommands();
        this.femaleVoice = null;
        
        this.init();
    }

    init() {
        this.setupSpeechRecognition();
        this.setupVoiceSelection();
        this.bindEvents();
        this.requestMicrophonePermission();
    }

    setupSpeechRecognition() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('Speech recognition not supported in this browser');
            this.updateStatus('Voice not supported in this browser');
            return;
        }

        console.log('Speech recognition is supported');

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = 'en-US';

        this.recognition.onstart = () => {
            console.log('Speech recognition started');
            this.isListening = true;
            this.updateStatus('Listening - Say "Hey Assistant"');
            this.updateIndicator();
        };

        this.recognition.onend = () => {
            console.log('Speech recognition ended');
            this.isListening = false;
            this.updateIndicator();

            // Restart listening if not manually stopped
            if (!this.isAwake) {
                setTimeout(() => this.startListening(), 1000);
            }
        };

        this.recognition.onresult = (event) => {
            this.handleSpeechResult(event);
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            if (event.error === 'not-allowed') {
                this.showPermissionError();
                this.showPermissionToast();
            } else if (event.error === 'no-speech') {
                console.log('No speech detected, continuing to listen...');
            } else {
                console.log('Speech recognition error, will retry...');
            }
        };
    }

    setupVoiceSelection() {
        // Wait for voices to load
        if (this.synthesis.getVoices().length === 0) {
            this.synthesis.addEventListener('voiceschanged', () => {
                this.selectFemaleVoice();
            });
        } else {
            this.selectFemaleVoice();
        }
    }

    selectFemaleVoice() {
        const voices = this.synthesis.getVoices();
        
        // Priority order for soft, pleasant female voices
        const preferredVoices = [
            'Samantha', 'Victoria', 'Allison', 'Ava', 'Susan', 'Joanna', 'Salli', 'Kimberly',
            'Microsoft Zira', 'Microsoft Hazel', 'Google UK English Female', 'Google US English',
            'Alex', 'Karen', 'Moira', 'Tessa', 'Veena', 'Fiona'
        ];

        // Find the best female voice
        for (const preferredName of preferredVoices) {
            this.femaleVoice = voices.find(voice => 
                voice.name.includes(preferredName) && 
                voice.lang.startsWith('en')
            );
            if (this.femaleVoice) break;
        }

        // Fallback to any English female voice
        if (!this.femaleVoice) {
            this.femaleVoice = voices.find(voice => 
                voice.lang.startsWith('en') && 
                (voice.name.toLowerCase().includes('female') || 
                 voice.name.toLowerCase().includes('woman') ||
                 voice.name.toLowerCase().includes('zira') ||
                 voice.name.toLowerCase().includes('hazel'))
            );
        }

        // Final fallback to any English voice
        if (!this.femaleVoice) {
            this.femaleVoice = voices.find(voice => voice.lang.startsWith('en'));
        }

        console.log('Selected voice:', this.femaleVoice?.name || 'Default');
    }

    initializeCommands() {
        return {
            'read this post': () => this.readCurrentPost(),
            'read this page': () => this.readCurrentPost(),
            'read aloud': () => this.readCurrentPost(),
            'stop reading': () => this.stopReading(),
            'show recent posts': () => this.showRecentPosts(),
            'show me recent posts': () => this.showRecentPosts(),
            'open admin': () => this.openAdmin(),
            'open admin panel': () => this.openAdmin(),
            'go to admin': () => this.openAdmin(),
            'search for': (query) => this.performSearch(query),
            'find': (query) => this.performSearch(query),
            'look for': (query) => this.performSearch(query),
            'go home': () => this.goHome(),
            'go to home': () => this.goHome(),
            'what time is it': () => this.tellTime(),
            'what is the time': () => this.tellTime(),
            'help': () => this.showHelp(),
            'what can you do': () => this.showHelp(),
            'cancel': () => this.cancelCommand(),
            'never mind': () => this.cancelCommand(),
            'goodbye': () => this.sayGoodbye(),
            'bye': () => this.sayGoodbye(),
            'thank you': () => this.sayYoureWelcome(),
            'thanks': () => this.sayYoureWelcome()
        };
    }

    async requestMicrophonePermission() {
        try {
            // Check if we already have permission
            const permission = await navigator.permissions.query({ name: 'microphone' });

            if (permission.state === 'granted') {
                console.log('Microphone permission already granted');
                this.onPermissionGranted();
                return;
            }

            // Request microphone permission
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop()); // Stop the stream immediately

            console.log('Microphone permission granted');
            this.onPermissionGranted();
        } catch (error) {
            console.error('Microphone permission denied:', error);
            this.showPermissionError();
            this.showPermissionToast();
        }
    }

    onPermissionGranted() {
        this.updateStatus('Microphone ready - Say "Hey Assistant"');
        this.updateIndicator();
        this.startListening();

        // Hide permission toast if it's showing
        const toastElement = document.getElementById('micPermissionToast');
        if (toastElement) {
            const toast = bootstrap.Toast.getInstance(toastElement);
            if (toast) {
                toast.hide();
            }
        }
    }

    bindEvents() {
        const indicator = document.getElementById('assistant-indicator');
        if (indicator) {
            indicator.addEventListener('click', () => {
                if (this.isAwake) {
                    this.stopVoiceAssistant();
                } else if (!this.isListening) {
                    // Try to start listening if not already
                    this.requestMicrophonePermission();
                } else {
                    this.wakeUp();
                }
            });
        }
    }

    startListening() {
        if (!this.recognition) {
            console.log('Speech recognition not available');
            return;
        }

        if (this.isListening) {
            console.log('Already listening');
            return;
        }

        try {
            console.log('Starting speech recognition...');
            this.recognition.start();
        } catch (error) {
            console.error('Failed to start speech recognition:', error);
            if (error.name === 'InvalidStateError') {
                // Recognition is already running, stop and restart
                this.recognition.stop();
                setTimeout(() => this.startListening(), 500);
            } else if (error.name === 'NotAllowedError') {
                this.showPermissionError();
                this.showPermissionToast();
            }
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    handleSpeechResult(event) {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript.toLowerCase().trim();
            
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }

        const fullTranscript = (finalTranscript + interimTranscript).toLowerCase();

        if (!this.isAwake) {
            // Check for wake words
            if (this.wakeWords.some(wake => fullTranscript.includes(wake))) {
                this.wakeUp();
                return;
            }
        } else {
            // Process commands when awake
            if (finalTranscript) {
                this.processCommand(finalTranscript);
            }
        }
    }

    wakeUp() {
        this.isAwake = true;
        this.stopListening();
        this.updateIndicator();
        this.showModal();
        this.speak("Hello! How can I help you today?");
        
        // Start listening for commands
        setTimeout(() => {
            this.startListening();
        }, 2000);
    }

    processCommand(transcript) {
        console.log('Processing command:', transcript);
        
        // Find matching command
        for (const [command, action] of Object.entries(this.commands)) {
            if (transcript.includes(command)) {
                // Extract additional parameters for search commands
                if (command === 'search for' || command === 'find' || command === 'look for') {
                    const query = transcript.replace(command, '').trim();
                    if (query) {
                        action(query);
                    } else {
                        this.speak("What would you like me to search for?");
                    }
                } else {
                    action();
                }
                return;
            }
        }

        // No command found
        this.speak("I'm sorry, I didn't understand that command. Try saying 'help' to see what I can do.");
    }

    speak(text, callback = null) {
        // Stop any current speech
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        
        // Configure for soft, sexy female voice
        if (this.femaleVoice) {
            utterance.voice = this.femaleVoice;
        }
        
        utterance.rate = 0.9;    // Slightly slower for more pleasant delivery
        utterance.pitch = 1.1;   // Higher pitch for feminine voice
        utterance.volume = 0.9;  // Soft volume

        utterance.onend = () => {
            if (callback) callback();
        };

        this.currentUtterance = utterance;
        this.synthesis.speak(utterance);
    }

    updateStatus(message) {
        const status = document.getElementById('assistant-status');
        if (status) {
            status.textContent = message;
            status.style.color = ''; // Reset color
        }
    }

    updateIndicator() {
        const indicator = document.getElementById('assistant-indicator');
        const status = document.getElementById('assistant-status');
        const icon = document.getElementById('assistant-icon');

        if (!indicator || !status || !icon) return;

        if (this.isAwake) {
            indicator.classList.add('listening');
            status.textContent = 'Listening for commands...';
            icon.className = 'fas fa-microphone-alt';
        } else if (this.isListening) {
            indicator.classList.add('listening');
            status.textContent = 'Say "Hey Assistant" to wake me up';
            icon.className = 'fas fa-microphone';
        } else {
            indicator.classList.remove('listening');
            status.textContent = 'Click to activate voice assistant';
            icon.className = 'fas fa-microphone-slash';
        }
    }

    showModal() {
        const modal = new bootstrap.Modal(document.getElementById('voiceAssistantModal'));
        modal.show();
    }

    hideModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('voiceAssistantModal'));
        if (modal) {
            modal.hide();
        }
    }

    // Command implementations
    readCurrentPost() {
        const readAloudBtn = document.getElementById('read-aloud-btn') || document.getElementById('sidebar-read-aloud-btn');
        if (readAloudBtn) {
            this.speak("Starting to read the content for you.");
            setTimeout(() => {
                readAloudBtn.click();
                this.stopVoiceAssistant();
            }, 1500);
        } else {
            this.speak("I'm sorry, I can't find any content to read on this page.");
        }
    }

    stopReading() {
        // Stop text-to-speech if active
        if (window.speechSynthesis) {
            window.speechSynthesis.cancel();
        }

        // Stop read aloud if active
        if (typeof stopReading === 'function') {
            stopReading();
        }

        this.speak("I've stopped reading.");
        this.stopVoiceAssistant();
    }

    showRecentPosts() {
        this.speak("Taking you to the recent posts.");
        setTimeout(() => {
            window.location.href = '/';
            this.stopVoiceAssistant();
        }, 1500);
    }

    openAdmin() {
        this.speak("Opening the admin panel for you.");
        setTimeout(() => {
            window.location.href = '/admin';
            this.stopVoiceAssistant();
        }, 1500);
    }

    performSearch(query) {
        this.speak(`Searching for ${query}.`);
        setTimeout(() => {
            // Use the chat to search
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.value = `Search for: ${query}`;
                chatInput.focus();

                // Trigger search
                const sendBtn = document.querySelector('.chat-send-btn');
                if (sendBtn) {
                    sendBtn.click();
                }

                // Scroll to chat
                document.querySelector('.chat-container')?.scrollIntoView({
                    behavior: 'smooth'
                });
            }
            this.stopVoiceAssistant();
        }, 1500);
    }

    goHome() {
        this.speak("Taking you to the home page.");
        setTimeout(() => {
            window.location.href = '/';
            this.stopVoiceAssistant();
        }, 1500);
    }

    tellTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        this.speak(`The current time is ${timeString}.`);
        setTimeout(() => this.stopVoiceAssistant(), 3000);
    }

    showHelp() {
        const helpText = `I can help you with several commands. You can say:
        "Read this post" to have me read the current content aloud,
        "Show recent posts" to go to the home page,
        "Open admin" to access the admin panel,
        "Search for" followed by your query to search the site,
        "What time is it" to get the current time,
        or "Go home" to return to the main page.
        Just say "Hey Assistant" anytime to wake me up!`;

        this.speak(helpText);
        setTimeout(() => this.stopVoiceAssistant(), 15000);
    }

    cancelCommand() {
        this.speak("Okay, canceling that.");
        this.stopVoiceAssistant();
    }

    sayGoodbye() {
        this.speak("Goodbye! Say 'Hey Assistant' anytime you need me.");
        this.stopVoiceAssistant();
    }

    sayYoureWelcome() {
        this.speak("You're very welcome! Happy to help.");
        setTimeout(() => this.stopVoiceAssistant(), 2000);
    }

    stopVoiceAssistant() {
        this.isAwake = false;
        this.stopListening();
        this.hideModal();
        this.updateIndicator();

        // Restart passive listening after a delay
        setTimeout(() => {
            this.startListening();
        }, 2000);
    }

    showPermissionError() {
        const status = document.getElementById('assistant-status');
        const indicator = document.getElementById('assistant-indicator');
        const icon = document.getElementById('assistant-icon');

        if (status) {
            status.textContent = 'Click to enable microphone';
            status.style.color = '#dc3545';
        }

        if (indicator) {
            indicator.classList.remove('listening');
        }

        if (icon) {
            icon.className = 'fas fa-microphone-slash';
        }

        // Show a more helpful message
        console.log('To enable voice assistant:');
        console.log('1. Click the microphone icon in your browser address bar');
        console.log('2. Allow microphone access for this site');
        console.log('3. Refresh the page');
    }

    showPermissionToast() {
        const toastElement = document.getElementById('micPermissionToast');
        if (toastElement) {
            const toast = new bootstrap.Toast(toastElement, {
                autohide: false // Don't auto-hide, let user dismiss
            });
            toast.show();
        }
    }

    hideAssistant() {
        const assistant = document.getElementById('voice-assistant');
        if (assistant) {
            assistant.style.display = 'none';
        }
    }
}

// Global functions for template access
let voiceAssistant = null;

function initializeVoiceAssistant() {
    voiceAssistant = new VoiceAssistant();
    window.voiceAssistant = voiceAssistant; // Make globally accessible
}

function stopVoiceAssistant() {
    if (voiceAssistant) {
        voiceAssistant.stopVoiceAssistant();
    }
}

function executeVoiceCommand(command) {
    if (voiceAssistant) {
        voiceAssistant.processCommand(command);
    }
}

// Debug function - call this in browser console to check status
function debugVoiceAssistant() {
    console.log('=== Voice Assistant Debug Info ===');
    console.log('Voice Assistant exists:', !!window.voiceAssistant);
    console.log('Speech Recognition supported:', 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window);
    console.log('Is listening:', window.voiceAssistant?.isListening);
    console.log('Is awake:', window.voiceAssistant?.isAwake);

    if (window.voiceAssistant) {
        console.log('Recognition object:', !!window.voiceAssistant.recognition);

        // Check microphone permission
        navigator.permissions.query({ name: 'microphone' }).then(permission => {
            console.log('Microphone permission:', permission.state);
        }).catch(err => {
            console.log('Could not check microphone permission:', err);
        });
    }

    console.log('=== End Debug Info ===');
}
