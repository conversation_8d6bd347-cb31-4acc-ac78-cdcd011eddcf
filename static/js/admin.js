// Admin Panel JavaScript for Markdown CMS

// Global variables
let editor = null;
let isUnsavedChanges = false;

// Initialize admin functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeEditor();
    initializeFileUpload();
    initializeFormValidation();
    initializeUnsavedChangesWarning();
    initializeTooltips();
    initializeConfirmDialogs();
});

// Initialize CodeMirror editor if available
function initializeEditor() {
    const textarea = document.getElementById('content');
    if (textarea && typeof CodeMirror !== 'undefined') {
        editor = CodeMirror.fromTextArea(textarea, {
            mode: 'markdown',
            theme: 'github',
            lineNumbers: true,
            lineWrapping: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            extraKeys: {
                'Ctrl-S': function() {
                    document.getElementById('post-form').dispatchEvent(new Event('submit'));
                },
                'Cmd-S': function() {
                    document.getElementById('post-form').dispatchEvent(new Event('submit'));
                },
                'Ctrl-B': function() {
                    insertMarkdown('**', '**');
                },
                'Cmd-B': function() {
                    insertMarkdown('**', '**');
                },
                'Ctrl-I': function() {
                    insertMarkdown('*', '*');
                },
                'Cmd-I': function() {
                    insertMarkdown('*', '*');
                }
            }
        });
        
        editor.on('change', function() {
            isUnsavedChanges = true;
        });
    }
}

// File upload with drag and drop
function initializeFileUpload() {
    const uploadArea = document.querySelector('.upload-area');
    if (!uploadArea) return;
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });
    
    // Click to upload
    uploadArea.addEventListener('click', function() {
        const fileInput = document.getElementById('file-upload');
        if (fileInput) {
            fileInput.click();
        }
    });
}

// Handle file upload
function handleFileUpload(file) {
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    
    const progressContainer = document.getElementById('upload-progress');
    const progressBar = progressContainer?.querySelector('.progress-bar');
    
    if (progressContainer) {
        progressContainer.style.display = 'block';
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.classList.remove('bg-success', 'bg-danger');
        }
    }
    
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.url) {
            // Insert into editor
            const isImage = file.type.startsWith('image/');
            const markdown = isImage ? 
                `![${file.name}](${data.url})` : 
                `[${file.name}](${data.url})`;
            
            if (editor) {
                editor.replaceSelection(markdown);
            } else {
                const textarea = document.getElementById('content');
                if (textarea) {
                    textarea.value += '\n' + markdown;
                }
            }
            
            // Show success
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.classList.add('bg-success');
            }
            
            showNotification('File uploaded successfully!', 'success');
            
            setTimeout(() => {
                if (progressContainer) {
                    progressContainer.style.display = 'none';
                }
            }, 2000);
        } else {
            throw new Error(data.error || 'Upload failed');
        }
    })
    .catch(error => {
        if (progressBar) {
            progressBar.classList.add('bg-danger');
        }
        showNotification('Upload failed: ' + error.message, 'danger');
        
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    });
}

// Form validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                e.stopPropagation();
            }
            this.classList.add('was-validated');
        });
    });
}

function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Unsaved changes warning
function initializeUnsavedChangesWarning() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                isUnsavedChanges = true;
            });
        });
        
        form.addEventListener('submit', function() {
            isUnsavedChanges = false;
        });
    });
    
    window.addEventListener('beforeunload', function(e) {
        if (isUnsavedChanges) {
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return e.returnValue;
        }
    });
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Confirm dialogs
function initializeConfirmDialogs() {
    const confirmButtons = document.querySelectorAll('[data-confirm]');
    
    confirmButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.dataset.confirm;
            if (!confirm(message)) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    });
}

// Markdown editor functions
function insertMarkdown(before, after) {
    if (editor) {
        const selection = editor.getSelection();
        const replacement = before + selection + after;
        editor.replaceSelection(replacement);
        
        if (!selection) {
            const cursor = editor.getCursor();
            editor.setCursor(cursor.line, cursor.ch - after.length);
        }
        
        editor.focus();
    } else {
        const textarea = document.getElementById('content');
        if (textarea) {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const selectedText = textarea.value.substring(start, end);
            
            const newText = before + selectedText + after;
            textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
            
            const newCursorPos = start + before.length + selectedText.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            textarea.focus();
        }
    }
    
    isUnsavedChanges = true;
}

function insertLink() {
    const url = prompt('Enter URL:');
    if (url) {
        const text = prompt('Enter link text:', url);
        insertMarkdown(`[${text || url}](`, `${url})`);
    }
}

function insertImage() {
    const url = prompt('Enter image URL:');
    if (url) {
        const alt = prompt('Enter alt text:', 'Image');
        insertMarkdown(`![${alt}](`, `${url})`);
    }
}

function insertTable() {
    const rows = parseInt(prompt('Number of rows:', '3'));
    const cols = parseInt(prompt('Number of columns:', '3'));
    
    if (rows && cols) {
        let table = '\n';
        
        // Header row
        table += '|';
        for (let i = 0; i < cols; i++) {
            table += ` Header ${i + 1} |`;
        }
        table += '\n';
        
        // Separator row
        table += '|';
        for (let i = 0; i < cols; i++) {
            table += ' --- |';
        }
        table += '\n';
        
        // Data rows
        for (let r = 0; r < rows - 1; r++) {
            table += '|';
            for (let c = 0; c < cols; c++) {
                table += ` Cell ${r + 1},${c + 1} |`;
            }
            table += '\n';
        }
        
        table += '\n';
        
        if (editor) {
            editor.replaceSelection(table);
        } else {
            const textarea = document.getElementById('content');
            if (textarea) {
                textarea.value += table;
            }
        }
        
        isUnsavedChanges = true;
    }
}

// Preview functionality
function togglePreview() {
    const textarea = document.getElementById('content');
    const preview = document.getElementById('preview-content');
    const btn = document.getElementById('preview-btn');
    
    if (!textarea || !preview || !btn) return;
    
    const isPreviewMode = preview.style.display !== 'none';
    
    if (isPreviewMode) {
        // Show editor
        if (editor) {
            editor.getWrapperElement().style.display = 'block';
        } else {
            textarea.style.display = 'block';
        }
        preview.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-eye"></i> Preview';
    } else {
        // Show preview
        if (editor) {
            editor.getWrapperElement().style.display = 'none';
        } else {
            textarea.style.display = 'none';
        }
        preview.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-edit"></i> Edit';
        
        // Convert markdown to HTML
        const content = editor ? editor.getValue() : textarea.value;
        preview.innerHTML = convertMarkdownToHTML(content);
    }
}

// Simple markdown to HTML converter
function convertMarkdownToHTML(markdown) {
    return markdown
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gim, '<em>$1</em>')
        .replace(/`(.*?)`/gim, '<code>$1</code>')
        .replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img alt="$1" src="$2" class="img-fluid">')
        .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
        .replace(/^\> (.+)/gim, '<blockquote class="blockquote">$1</blockquote>')
        .replace(/\n\n/gim, '</p><p>')
        .replace(/\n/gim, '<br>')
        .replace(/^(.+)/gim, '<p>$1</p>');
}

// Utility functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function setLoadingState(element, loading = true) {
    if (loading) {
        element.classList.add('loading');
        element.disabled = true;
    } else {
        element.classList.remove('loading');
        element.disabled = false;
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Auto-save functionality
function initializeAutoSave() {
    const form = document.getElementById('post-form');
    if (!form) return;
    
    const autoSave = debounce(() => {
        if (isUnsavedChanges) {
            saveAsDraft();
        }
    }, 30000); // Auto-save every 30 seconds
    
    if (editor) {
        editor.on('change', autoSave);
    } else {
        const textarea = document.getElementById('content');
        if (textarea) {
            textarea.addEventListener('input', autoSave);
        }
    }
}

function saveAsDraft() {
    const form = document.getElementById('post-form');
    if (!form) return;
    
    const formData = new FormData(form);
    const data = {
        title: formData.get('title'),
        content: editor ? editor.getValue() : formData.get('content'),
        slug: formData.get('slug'),
        published: false,
        tags: formData.get('tags') ? formData.get('tags').split(',') : []
    };
    
    fetch(form.action, {
        method: form.method || 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.slug) {
            isUnsavedChanges = false;
            showNotification('Draft saved automatically', 'success');
        }
    })
    .catch(error => {
        console.error('Auto-save failed:', error);
    });
}

// Export functions for global use
window.AdminCMS = {
    insertMarkdown,
    insertLink,
    insertImage,
    insertTable,
    togglePreview,
    showNotification,
    setLoadingState,
    handleFileUpload
};
