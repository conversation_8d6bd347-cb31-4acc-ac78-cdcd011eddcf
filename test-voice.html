<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Assistant Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .test-btn:hover { background: #0056b3; }
        .test-btn:disabled { background: #6c757d; cursor: not-allowed; }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎙️ Voice Assistant Test Page</h1>
        
        <div id="browser-support" class="status info">
            Checking browser support...
        </div>
        
        <div id="microphone-status" class="status info">
            Checking microphone permissions...
        </div>
        
        <div class="test-controls">
            <button id="test-speech-recognition" class="test-btn">Test Speech Recognition</button>
            <button id="test-speech-synthesis" class="test-btn">Test Speech Synthesis</button>
            <button id="request-mic-permission" class="test-btn">Request Microphone Permission</button>
            <button id="start-listening" class="test-btn" disabled>Start Listening</button>
            <button id="stop-listening" class="test-btn" disabled>Stop Listening</button>
        </div>
        
        <h3>Console Output:</h3>
        <div id="console-output"></div>
        
        <h3>Instructions:</h3>
        <ol>
            <li><strong>Check browser support</strong> - Should show green if supported</li>
            <li><strong>Request microphone permission</strong> - Click the button and allow access</li>
            <li><strong>Test speech synthesis</strong> - Should hear a female voice</li>
            <li><strong>Start listening</strong> - Try saying "Hey Assistant"</li>
        </ol>
    </div>

    <script>
        // Console logging override
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '✅';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // Test variables
        let recognition = null;
        let isListening = false;
        
        // Check browser support
        function checkBrowserSupport() {
            const speechRecognitionSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
            const speechSynthesisSupported = 'speechSynthesis' in window;
            
            const supportDiv = document.getElementById('browser-support');
            
            if (speechRecognitionSupported && speechSynthesisSupported) {
                supportDiv.className = 'status success';
                supportDiv.textContent = '✅ Browser supports both speech recognition and synthesis';
                console.log('Browser support: FULL');
                return true;
            } else {
                supportDiv.className = 'status error';
                supportDiv.textContent = `❌ Missing support - Recognition: ${speechRecognitionSupported}, Synthesis: ${speechSynthesisSupported}`;
                console.error('Browser support: LIMITED');
                return false;
            }
        }
        
        // Check microphone permissions
        async function checkMicrophonePermissions() {
            const statusDiv = document.getElementById('microphone-status');
            
            try {
                const permission = await navigator.permissions.query({ name: 'microphone' });
                console.log('Microphone permission state:', permission.state);
                
                if (permission.state === 'granted') {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Microphone permission granted';
                    enableListeningButtons();
                } else if (permission.state === 'denied') {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ Microphone permission denied';
                } else {
                    statusDiv.className = 'status info';
                    statusDiv.textContent = '⏳ Microphone permission not yet requested';
                }
            } catch (error) {
                console.error('Error checking microphone permissions:', error);
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Could not check microphone permissions';
            }
        }
        
        // Request microphone permission
        async function requestMicrophonePermission() {
            console.log('Requesting microphone permission...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                
                console.log('Microphone permission granted!');
                const statusDiv = document.getElementById('microphone-status');
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ Microphone permission granted';
                enableListeningButtons();
                
            } catch (error) {
                console.error('Microphone permission denied:', error);
                const statusDiv = document.getElementById('microphone-status');
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Microphone permission denied';
            }
        }
        
        // Enable listening buttons
        function enableListeningButtons() {
            document.getElementById('start-listening').disabled = false;
            document.getElementById('test-speech-recognition').disabled = false;
        }
        
        // Test speech synthesis
        function testSpeechSynthesis() {
            console.log('Testing speech synthesis...');
            
            const utterance = new SpeechSynthesisUtterance('Hello! This is a test of the speech synthesis with a soft female voice.');
            
            // Try to find a female voice
            const voices = speechSynthesis.getVoices();
            const femaleVoice = voices.find(voice => 
                voice.name.includes('Samantha') || 
                voice.name.includes('Victoria') || 
                voice.name.includes('Zira') || 
                voice.name.includes('Hazel') ||
                voice.lang.startsWith('en')
            );
            
            if (femaleVoice) {
                utterance.voice = femaleVoice;
                console.log('Using voice:', femaleVoice.name);
            }
            
            utterance.rate = 0.85;
            utterance.pitch = 1.1;
            utterance.volume = 0.9;
            
            utterance.onstart = () => console.log('Speech synthesis started');
            utterance.onend = () => console.log('Speech synthesis ended');
            utterance.onerror = (e) => console.error('Speech synthesis error:', e);
            
            speechSynthesis.speak(utterance);
        }
        
        // Test speech recognition
        function testSpeechRecognition() {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                console.error('Speech recognition not supported');
                return;
            }
            
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();
            
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'en-US';
            
            recognition.onstart = () => {
                console.log('Speech recognition started - try saying "Hey Assistant"');
                isListening = true;
                document.getElementById('start-listening').disabled = true;
                document.getElementById('stop-listening').disabled = false;
            };
            
            recognition.onresult = (event) => {
                let transcript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    transcript += event.results[i][0].transcript;
                }
                console.log('Heard:', transcript.toLowerCase());
                
                if (transcript.toLowerCase().includes('hey assistant') || transcript.toLowerCase().includes('assistant')) {
                    console.log('🎉 Wake word detected!');
                    testSpeechSynthesis();
                }
            };
            
            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
            };
            
            recognition.onend = () => {
                console.log('Speech recognition ended');
                isListening = false;
                document.getElementById('start-listening').disabled = false;
                document.getElementById('stop-listening').disabled = true;
            };
            
            recognition.start();
        }
        
        // Stop listening
        function stopListening() {
            if (recognition && isListening) {
                recognition.stop();
                console.log('Stopping speech recognition...');
            }
        }
        
        // Event listeners
        document.getElementById('test-speech-recognition').addEventListener('click', testSpeechRecognition);
        document.getElementById('test-speech-synthesis').addEventListener('click', testSpeechSynthesis);
        document.getElementById('request-mic-permission').addEventListener('click', requestMicrophonePermission);
        document.getElementById('start-listening').addEventListener('click', testSpeechRecognition);
        document.getElementById('stop-listening').addEventListener('click', stopListening);
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Voice Assistant Test Page loaded');
            checkBrowserSupport();
            checkMicrophonePermissions();
            
            // Load voices when available
            if (speechSynthesis.getVoices().length === 0) {
                speechSynthesis.addEventListener('voiceschanged', () => {
                    console.log('Voices loaded:', speechSynthesis.getVoices().length);
                });
            }
        });
    </script>
</body>
</html>
