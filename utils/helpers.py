"""
Helper utilities for the CMS
"""

import os
import re
import mimetypes
from typing import List, Optional, Dict, Any
from datetime import datetime

# Allowed file extensions for uploads
ALLOWED_EXTENSIONS = {
    'images': {'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'},
    'documents': {'pdf', 'doc', 'docx', 'txt', 'md'},
    'media': {'mp4', 'mp3', 'wav', 'ogg'},
    'archives': {'zip', 'tar', 'gz'}
}

def allowed_file(filename: str) -> bool:
    """Check if file extension is allowed"""
    if '.' not in filename:
        return False
    
    ext = filename.rsplit('.', 1)[1].lower()
    
    # Check against all allowed extensions
    for category, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return True
    
    return False

def get_file_extension(filename: str) -> Optional[str]:
    """Get file extension from filename"""
    if '.' not in filename:
        return None
    
    return filename.rsplit('.', 1)[1].lower()

def get_file_category(filename: str) -> Optional[str]:
    """Get file category based on extension"""
    ext = get_file_extension(filename)
    if not ext:
        return None
    
    for category, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return category
    
    return None

def get_mime_type(filename: str) -> str:
    """Get MIME type for file"""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or 'application/octet-stream'

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace unsafe characters
    filename = re.sub(r'[^\w\s.-]', '', filename)
    filename = re.sub(r'[-\s]+', '-', filename)
    return filename.strip('-')

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def slugify(text: str) -> str:
    """Convert text to URL-friendly slug"""
    # Convert to lowercase and replace spaces with hyphens
    slug = re.sub(r'[^\w\s-]', '', text.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')

def extract_tags_from_text(text: str) -> List[str]:
    """Extract hashtags from text"""
    hashtag_pattern = r'#(\w+)'
    tags = re.findall(hashtag_pattern, text)
    return list(set(tags))  # Remove duplicates

def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M") -> str:
    """Format datetime for display"""
    return dt.strftime(format_str)

def time_ago(dt: datetime) -> str:
    """Get human-readable time difference"""
    now = datetime.now()
    diff = now - dt
    
    seconds = diff.total_seconds()
    
    if seconds < 60:
        return "just now"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    elif seconds < 86400:
        hours = int(seconds // 3600)
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif seconds < 2592000:  # 30 days
        days = int(seconds // 86400)
        return f"{days} day{'s' if days != 1 else ''} ago"
    elif seconds < 31536000:  # 365 days
        months = int(seconds // 2592000)
        return f"{months} month{'s' if months != 1 else ''} ago"
    else:
        years = int(seconds // 31536000)
        return f"{years} year{'s' if years != 1 else ''} ago"

def validate_email(email: str) -> bool:
    """Validate email address format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_url(url: str) -> bool:
    """Validate URL format"""
    pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    return re.match(pattern, url) is not None

def clean_html(text: str) -> str:
    """Remove HTML tags from text"""
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

def highlight_search_terms(text: str, search_terms: List[str], 
                          highlight_class: str = "highlight") -> str:
    """Highlight search terms in text"""
    if not search_terms:
        return text
    
    # Escape special regex characters in search terms
    escaped_terms = [re.escape(term) for term in search_terms]
    pattern = '|'.join(escaped_terms)
    
    def replace_func(match):
        return f'<span class="{highlight_class}">{match.group()}</span>'
    
    return re.sub(pattern, replace_func, text, flags=re.IGNORECASE)

def generate_excerpt(text: str, max_length: int = 150, 
                    search_terms: Optional[List[str]] = None) -> str:
    """Generate excerpt from text, optionally highlighting search terms"""
    # Clean HTML first
    clean_text = clean_html(text)
    
    # If search terms provided, try to find excerpt around first match
    if search_terms:
        for term in search_terms:
            match = re.search(re.escape(term), clean_text, re.IGNORECASE)
            if match:
                start = max(0, match.start() - max_length // 2)
                end = min(len(clean_text), start + max_length)
                excerpt = clean_text[start:end]
                
                # Adjust to word boundaries
                if start > 0:
                    first_space = excerpt.find(' ')
                    if first_space > 0:
                        excerpt = excerpt[first_space + 1:]
                
                if end < len(clean_text):
                    last_space = excerpt.rfind(' ')
                    if last_space > 0:
                        excerpt = excerpt[:last_space]
                
                return excerpt + "..."
    
    # Default excerpt from beginning
    return truncate_text(clean_text, max_length)

def parse_frontmatter_date(date_str: str) -> Optional[datetime]:
    """Parse date from frontmatter in various formats"""
    formats = [
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%SZ",
        "%Y/%m/%d",
        "%d/%m/%Y",
        "%m/%d/%Y"
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    return None

def get_reading_time(text: str, words_per_minute: int = 200) -> int:
    """Estimate reading time in minutes"""
    # Remove HTML and count words
    clean_text = clean_html(text)
    word_count = len(clean_text.split())
    
    reading_time = max(1, round(word_count / words_per_minute))
    return reading_time

def create_breadcrumbs(path: str, base_url: str = "") -> List[Dict[str, str]]:
    """Create breadcrumb navigation from path"""
    breadcrumbs = [{"name": "Home", "url": base_url or "/"}]
    
    if not path or path == "/":
        return breadcrumbs
    
    parts = [p for p in path.split("/") if p]
    current_path = ""
    
    for part in parts:
        current_path += "/" + part
        name = part.replace("-", " ").replace("_", " ").title()
        breadcrumbs.append({
            "name": name,
            "url": base_url + current_path
        })
    
    return breadcrumbs

def paginate_items(items: List[Any], page: int = 1, per_page: int = 10) -> Dict[str, Any]:
    """Paginate a list of items"""
    total_items = len(items)
    total_pages = (total_items + per_page - 1) // per_page
    
    # Ensure page is within bounds
    page = max(1, min(page, total_pages))
    
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    
    return {
        "items": items[start_idx:end_idx],
        "page": page,
        "per_page": per_page,
        "total_items": total_items,
        "total_pages": total_pages,
        "has_prev": page > 1,
        "has_next": page < total_pages,
        "prev_page": page - 1 if page > 1 else None,
        "next_page": page + 1 if page < total_pages else None
    }
