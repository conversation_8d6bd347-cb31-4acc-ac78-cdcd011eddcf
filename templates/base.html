<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Markdown CMS{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Highlight.js for code syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-book-open me-2"></i>
                Markdown CMS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Home</a>
                    </li>
                    {% if pages %}
                        {% for page in pages %}
                            {% if page.published %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('view_page', slug=page.slug) }}">{{ page.title }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                            <i class="fas fa-cog me-1"></i>
                            Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container-fluid">
        <div class="row">
            <!-- Content Area -->
            <div class="col-lg-8 col-xl-9">
                <div class="content-area p-4">
                    {% block content %}{% endblock %}
                </div>
            </div>
            
            <!-- Sidebar with Chat -->
            <div class="col-lg-4 col-xl-3">
                <div class="sidebar p-4">
                    {% block sidebar %}
                        <!-- AI Chat Interface -->
                        <div class="chat-container">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-robot me-2"></i>
                                        Chat with AI Host
                                    </h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="chat-messages" class="chat-messages p-3" style="height: 300px; overflow-y: auto;">
                                        <div class="message ai-message">
                                            <div class="message-content">
                                                <i class="fas fa-robot me-2"></i>
                                                Hello! I'm your friendly AI talk show host. I'm here to help you explore this site and have engaging conversations about the content. What would you like to know?
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chat-input p-3 border-top">
                                        <div class="input-group">
                                            <input type="text" id="chat-input" class="form-control" placeholder="Type your message...">
                                            <button class="btn btn-info" id="send-btn" type="button">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        </div>
                                        <div class="chat-suggestions mt-2">
                                            <div class="d-flex flex-wrap gap-1 mb-2">
                                                <button class="btn btn-outline-info btn-sm" onclick="quickPrompt('Create a post about web development')">
                                                    <i class="fas fa-plus me-1"></i>Web Dev Post
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="quickPrompt('Write an article about AI and technology')">
                                                    <i class="fas fa-robot me-1"></i>AI Article
                                                </button>
                                                <button class="btn btn-outline-warning btn-sm" onclick="quickPrompt('Create a tutorial on how to use this CMS')">
                                                    <i class="fas fa-book me-1"></i>Tutorial
                                                </button>
                                            </div>
                                            <small class="text-muted">Quick actions or type: "Create a post about..." or "Write an article on..."</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Posts -->
                        {% if posts %}
                            <div class="recent-posts mt-4">
                                <h5>Recent Posts</h5>
                                <div class="list-group">
                                    {% for post in posts[:3] %}
                                        {% if post.published %}
                                            <a href="{{ url_for('view_post', slug=post.slug) }}" class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">{{ post.title }}</h6>
                                                    <small>{{ post.created_at.strftime('%b %d') }}</small>
                                                </div>
                                                <p class="mb-1">{{ post.excerpt }}</p>
                                                {% if post.tags %}
                                                    <div class="tags">
                                                        {% for tag in post.tags[:3] %}
                                                            <span class="badge bg-secondary me-1">#{{ tag }}</span>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    {% endblock %}
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Markdown CMS</h5>
                    <p class="mb-0">A conversational content management system powered by Markdown and AI.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-heart text-danger"></i>
                        Built with Flask and AI
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Highlight.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <script>
        // Initialize syntax highlighting
        hljs.highlightAll();
        
        // Initialize chat functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeChat();
        });
    </script>
</body>
</html>
