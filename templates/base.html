<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Markdown CMS{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Highlight.js for code syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-book-open me-2"></i>
                Markdown CMS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Home</a>
                    </li>
                    {% if pages %}
                        {% for page in pages %}
                            {% if page.published %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('view_page', slug=page.slug) }}">{{ page.title }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                            <i class="fas fa-cog me-1"></i>
                            Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container-fluid">
        <div class="row">
            <!-- Content Area -->
            <div class="col-lg-8 col-xl-9">
                <div class="content-area p-4">
                    {% block content %}{% endblock %}
                </div>
            </div>
            
            <!-- Sidebar with Chat -->
            <div class="col-lg-4 col-xl-3">
                <div class="sidebar p-4">
                    {% block sidebar %}
                        <!-- AI Chat Interface -->
                        <div class="chat-container">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-robot me-2"></i>
                                        Chat with AI Host
                                    </h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="chat-messages" class="chat-messages p-3" style="height: 300px; overflow-y: auto;">
                                        <div class="message ai-message">
                                            <div class="message-content">
                                                <i class="fas fa-robot me-2"></i>
                                                Hello! I'm your friendly AI talk show host. I'm here to help you explore this site and have engaging conversations about the content. What would you like to know?
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chat-input p-3 border-top">
                                        <div class="input-group">
                                            <input type="text" id="chat-input" class="form-control" placeholder="Type your message...">
                                            <button class="btn btn-info" id="send-btn" type="button">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        </div>
                                        <div class="chat-suggestions mt-2">
                                            <div class="d-flex flex-wrap gap-1 mb-2">
                                                <button class="btn btn-outline-info btn-sm" onclick="quickPrompt('Create a post about web development')">
                                                    <i class="fas fa-plus me-1"></i>Web Dev Post
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="quickPrompt('Write an article about AI and technology')">
                                                    <i class="fas fa-robot me-1"></i>AI Article
                                                </button>
                                                <button class="btn btn-outline-warning btn-sm" onclick="quickPrompt('Create a tutorial on how to use this CMS')">
                                                    <i class="fas fa-book me-1"></i>Tutorial
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="quickPrompt('List all posts')">
                                                    <i class="fas fa-list me-1"></i>List Posts
                                                </button>
                                            </div>
                                            <small class="text-muted">Quick actions or type: "Create a post about...", "Edit the post [slug]", or "List all posts"</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Posts -->
                        {% if posts %}
                            <div class="recent-posts mt-4">
                                <h5>Recent Posts</h5>
                                <div class="list-group">
                                    {% for post in posts[:3] %}
                                        {% if post.published %}
                                            <a href="{{ url_for('view_post', slug=post.slug) }}" class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">{{ post.title }}</h6>
                                                    <small>{{ post.created_at.strftime('%b %d') }}</small>
                                                </div>
                                                <p class="mb-1">{{ post.excerpt }}</p>
                                                {% if post.tags %}
                                                    <div class="tags">
                                                        {% for tag in post.tags[:3] %}
                                                            <span class="badge bg-secondary me-1">#{{ tag }}</span>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    {% endblock %}
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Markdown CMS</h5>
                    <p class="mb-0">A conversational content management system powered by Markdown and AI.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-heart text-danger"></i>
                        Built with Flask and AI
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Highlight.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>

    {% block extra_scripts %}{% endblock %}

    <!-- Voice Assistant -->
    <div id="voice-assistant" class="voice-assistant">
        <div class="assistant-indicator" id="assistant-indicator">
            <div class="pulse-ring"></div>
            <div class="assistant-icon">
                <i class="fas fa-microphone-slash" id="assistant-icon"></i>
            </div>
        </div>
        <div class="assistant-status" id="assistant-status">
            Loading voice assistant...
        </div>
    </div>

    <!-- Microphone Permission Toast -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100;">
        <div id="micPermissionToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-microphone text-primary me-2"></i>
                <strong class="me-auto">Voice Assistant</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                <p class="mb-2">To use voice commands:</p>
                <ol class="mb-2 small">
                    <li>Click the microphone icon in your browser's address bar</li>
                    <li>Select "Allow" for microphone access</li>
                    <li>Say "Hey Assistant" to wake me up!</li>
                </ol>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" onclick="requestMicPermission()">
                        <i class="fas fa-microphone me-1"></i>
                        Enable Voice Assistant
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshVoiceAssistant()">
                        <i class="fas fa-refresh me-1"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Voice Assistant Modal -->
    <div class="modal fade" id="voiceAssistantModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0 text-center">
                    <div class="w-100">
                        <div class="voice-animation mx-auto mb-3" id="voice-animation">
                            <div class="voice-wave"></div>
                            <div class="voice-wave"></div>
                            <div class="voice-wave"></div>
                            <div class="voice-wave"></div>
                            <div class="voice-wave"></div>
                        </div>
                        <h5 class="modal-title" id="assistant-modal-title">How can I help you?</h5>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <p class="text-muted mb-3" id="assistant-modal-status">Listening...</p>
                    <div class="assistant-suggestions" id="assistant-suggestions">
                        <div class="suggestion-chip" onclick="executeVoiceCommand('read this post')">
                            "Read this post"
                        </div>
                        <div class="suggestion-chip" onclick="executeVoiceCommand('show me recent posts')">
                            "Show recent posts"
                        </div>
                        <div class="suggestion-chip" onclick="executeVoiceCommand('open admin panel')">
                            "Open admin"
                        </div>
                        <div class="suggestion-chip" onclick="executeVoiceCommand('search for something')">
                            "Search for..."
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 justify-content-center">
                    <button type="button" class="btn btn-outline-secondary" onclick="stopVoiceAssistant()">
                        <i class="fas fa-times me-1"></i>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize syntax highlighting
        hljs.highlightAll();

        // Initialize chat functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeChat();

            // Initialize voice assistant with delay to ensure script is loaded
            setTimeout(() => {
                if (typeof initializeVoiceAssistant === 'function') {
                    console.log('Initializing voice assistant...');
                    initializeVoiceAssistant();
                } else {
                    console.error('initializeVoiceAssistant function not found');
                }
            }, 500);
        });

        // Global functions for voice assistant
        function requestMicPermission() {
            if (window.voiceAssistant) {
                window.voiceAssistant.requestMicrophonePermission();
            }

            // Hide the toast
            const toast = bootstrap.Toast.getInstance(document.getElementById('micPermissionToast'));
            if (toast) {
                toast.hide();
            }
        }

        function refreshVoiceAssistant() {
            console.log('Refreshing voice assistant...');
            if (window.voiceAssistant) {
                window.voiceAssistant.stopListening();
                setTimeout(() => {
                    window.voiceAssistant.requestMicrophonePermission();
                }, 500);
            }

            // Hide the toast
            const toast = bootstrap.Toast.getInstance(document.getElementById('micPermissionToast'));
            if (toast) {
                toast.hide();
            }
        }

        function executeVoiceCommand(command) {
            if (window.voiceAssistant) {
                window.voiceAssistant.processCommand(command);
            }
        }

        function stopVoiceAssistant() {
            if (window.voiceAssistant) {
                window.voiceAssistant.stopVoiceAssistant();
            }
        }

        function debugVoiceAssistant() {
            if (window.voiceAssistant) {
                console.log('Voice Assistant Debug Info:');
                console.log('- Is Listening:', window.voiceAssistant.isListening);
                console.log('- Is Awake:', window.voiceAssistant.isAwake);
                console.log('- Recognition:', !!window.voiceAssistant.recognition);
                console.log('- Female Voice:', window.voiceAssistant.femaleVoice?.name || 'None');
                console.log('- Available Voices:', speechSynthesis.getVoices().length);
            } else {
                console.log('Voice Assistant not initialized');
            }
        }
    </script>

    <!-- Voice Assistant Script -->
    <script src="{{ url_for('static', filename='js/voice-assistant.js') }}"></script>
</body>
</html>
