{% extends "base.html" %}

{% block title %}{{ post.title }} - Markdown CMS{% endblock %}

{% block content %}
<article class="post-content">
    <!-- Post Header -->
    <header class="post-header mb-4">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ post.title }}</li>
                </ol>
            </nav>
            
            {% if not post.published %}
                <span class="badge bg-warning">Draft</span>
            {% endif %}
        </div>
        
        <h1 class="post-title display-4 mb-3">{{ post.title }}</h1>
        
        <div class="post-meta d-flex flex-wrap align-items-center text-muted mb-4">
            <div class="me-4">
                <i class="fas fa-calendar me-1"></i>
                <time datetime="{{ post.created_at.isoformat() }}">
                    {{ post.created_at.strftime('%B %d, %Y') }}
                </time>
            </div>
            
            {% if post.updated_at != post.created_at %}
                <div class="me-4">
                    <i class="fas fa-edit me-1"></i>
                    Updated {{ post.updated_at.strftime('%B %d, %Y') }}
                </div>
            {% endif %}
            
            <div class="me-4">
                <i class="fas fa-clock me-1"></i>
                {{ (post.content.split()|length / 200)|round|int }} min read
            </div>
            
            <div class="admin-actions">
                <a href="{{ url_for('admin_edit_post', slug=post.slug) }}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-edit me-1"></i>
                    Edit
                </a>
            </div>
        </div>
        
        {% if post.tags %}
            <div class="post-tags mb-4">
                {% for tag in post.tags %}
                    <span class="badge bg-primary me-1">#{{ tag }}</span>
                {% endfor %}
            </div>
        {% endif %}
    </header>
    
    <!-- Post Content -->
    <div class="post-body">
        {{ post.content | markdown }}
    </div>
    
    <!-- Post Footer -->
    <footer class="post-footer mt-5 pt-4 border-top">
        <div class="row">
            <div class="col-md-6">
                <h5>Share this post</h5>
                <div class="share-buttons">
                    <a href="https://twitter.com/intent/tweet?text={{ post.title | urlencode }}&url={{ request.url | urlencode }}" 
                       class="btn btn-outline-info btn-sm me-2" target="_blank">
                        <i class="fab fa-twitter me-1"></i>
                        Twitter
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.url | urlencode }}" 
                       class="btn btn-outline-primary btn-sm me-2" target="_blank">
                        <i class="fab fa-facebook me-1"></i>
                        Facebook
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.url | urlencode }}" 
                       class="btn btn-outline-secondary btn-sm" target="_blank">
                        <i class="fab fa-linkedin me-1"></i>
                        LinkedIn
                    </a>
                </div>
            </div>
            
            <div class="col-md-6 text-md-end">
                <h5>Discuss with AI</h5>
                <p class="text-muted mb-2">Have questions about this post? Chat with our AI host!</p>
                <button class="btn btn-info btn-sm" onclick="startPostDiscussion()">
                    <i class="fas fa-comments me-1"></i>
                    Start Discussion
                </button>
            </div>
        </div>
    </footer>
</article>

<!-- Related Posts -->
{% if related_posts %}
<section class="related-posts mt-5">
    <h3 class="mb-4">Related Posts</h3>
    <div class="row">
        {% for related_post in related_posts[:3] %}
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <a href="{{ url_for('view_post', slug=related_post.slug) }}" class="text-decoration-none">
                                {{ related_post.title }}
                            </a>
                        </h6>
                        <p class="card-text small">{{ related_post.excerpt }}</p>
                        <small class="text-muted">{{ related_post.created_at.strftime('%b %d, %Y') }}</small>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</section>
{% endif %}
{% endblock %}

{% block sidebar %}
<!-- Table of Contents -->
<div class="toc-container mb-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                Table of Contents
            </h6>
        </div>
        <div class="card-body">
            <div id="table-of-contents">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Post Actions -->
<div class="post-actions mb-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-tools me-2"></i>
                Actions
            </h6>
        </div>
        <div class="card-body">
            <div class="d-grid gap-2">
                <a href="{{ url_for('admin_edit_post', slug=post.slug) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-edit me-1"></i>
                    Edit Post
                </a>
                <button class="btn btn-outline-info btn-sm" onclick="copyPostLink()">
                    <i class="fas fa-link me-1"></i>
                    Copy Link
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="printPost()">
                    <i class="fas fa-print me-1"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    generateTableOfContents();
});

function generateTableOfContents() {
    const headings = document.querySelectorAll('.post-body h1, .post-body h2, .post-body h3, .post-body h4, .post-body h5, .post-body h6');
    const tocContainer = document.getElementById('table-of-contents');
    
    if (headings.length === 0) {
        tocContainer.innerHTML = '<p class="text-muted small">No headings found</p>';
        return;
    }
    
    let tocHTML = '<ul class="list-unstyled">';
    
    headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;
        
        const level = parseInt(heading.tagName.charAt(1));
        const indent = (level - 1) * 15;
        
        tocHTML += `
            <li style="margin-left: ${indent}px;">
                <a href="#${id}" class="text-decoration-none small">
                    ${heading.textContent}
                </a>
            </li>
        `;
    });
    
    tocHTML += '</ul>';
    tocContainer.innerHTML = tocHTML;
}

function startPostDiscussion() {
    const chatInput = document.getElementById('chat-input');
    const postTitle = "{{ post.title }}";
    
    if (chatInput) {
        chatInput.value = `I'd like to discuss the post "${postTitle}". `;
        chatInput.focus();
        
        // Scroll to chat
        document.querySelector('.chat-container').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }
}

function copyPostLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        btn.classList.remove('btn-outline-info');
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-info');
        }, 2000);
    });
}

function printPost() {
    window.print();
}

// Smooth scrolling for TOC links
document.addEventListener('click', function(e) {
    if (e.target.matches('#table-of-contents a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});
</script>
{% endblock %}
