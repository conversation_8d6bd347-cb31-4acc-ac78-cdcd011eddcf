{% extends "base.html" %}

{% block title %}Home - Markdown CMS{% endblock %}

{% block content %}
<div class="hero-section mb-5">
    <div class="jumbotron bg-light p-5 rounded">
        <h1 class="display-4">Welcome to Markdown CMS</h1>
        <p class="lead">A modern, conversational content management system that combines the simplicity of Markdown with the power of AI.</p>
        <hr class="my-4">
        <p>Explore our content, chat with our AI host, and discover what makes this CMS special.</p>
        <a class="btn btn-primary btn-lg" href="#latest-posts" role="button">
            <i class="fas fa-arrow-down me-2"></i>
            Explore Content
        </a>
    </div>
</div>

<!-- Latest Posts -->
<section id="latest-posts" class="mb-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-newspaper me-2"></i>
            Latest Posts
        </h2>
        {% if posts|length > 5 %}
            <a href="/posts" class="btn btn-outline-primary">View All Posts</a>
        {% endif %}
    </div>
    
    {% if posts %}
        <div class="row">
            {% for post in posts %}
                {% if post.published %}
                    <div class="col-md-6 col-lg-12 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="card-title">
                                        <a href="{{ url_for('view_post', slug=post.slug) }}" class="text-decoration-none">
                                            {{ post.title }}
                                        </a>
                                    </h5>
                                    <small class="text-muted">{{ post.created_at.strftime('%b %d, %Y') }}</small>
                                </div>
                                
                                <p class="card-text">{{ post.excerpt }}</p>
                                
                                {% if post.tags %}
                                    <div class="tags mb-3">
                                        {% for tag in post.tags %}
                                            <span class="badge bg-secondary me-1">#{{ tag }}</span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('view_post', slug=post.slug) }}" class="btn btn-primary btn-sm">
                                        Read More
                                        <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ (post.content.split()|length / 200)|round|int }} min read
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No posts yet</h4>
            <p class="text-muted">Start creating content to see it here.</p>
            <a href="{{ url_for('admin_new_post') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create First Post
            </a>
        </div>
    {% endif %}
</section>

<!-- Features Section -->
<section class="features-section mb-5">
    <h2 class="mb-4">
        <i class="fas fa-star me-2"></i>
        Features
    </h2>
    
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <i class="fas fa-markdown fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Markdown Powered</h5>
                    <p class="card-text">Write content in simple Markdown format with full support for formatting, code blocks, and more.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <i class="fas fa-robot fa-3x text-info mb-3"></i>
                    <h5 class="card-title">AI Conversations</h5>
                    <p class="card-text">Chat with our AI host who can help you explore content and engage in meaningful discussions.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <i class="fas fa-tachometer-alt fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Fast & Simple</h5>
                    <p class="card-text">Lightweight, efficient, and easy to use. Focus on content creation, not complex configurations.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Getting Started -->
<section class="getting-started-section">
    <div class="card bg-light">
        <div class="card-body">
            <h3 class="card-title">
                <i class="fas fa-rocket me-2"></i>
                Getting Started
            </h3>
            <p class="card-text">Ready to start creating content? Here's how to get going:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <ol>
                        <li class="mb-2">
                            <strong>Create Content:</strong> Use the admin panel to create posts and pages in Markdown format.
                        </li>
                        <li class="mb-2">
                            <strong>Chat with AI:</strong> Try the chat interface on the right to see how our AI host works.
                        </li>
                        <li class="mb-2">
                            <strong>Customize:</strong> Modify templates and styles to match your brand.
                        </li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-cog me-2"></i>
                            Go to Admin Panel
                        </a>
                        <a href="{{ url_for('admin_new_post') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create Your First Post
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block sidebar %}
{{ super() }}

<!-- Quick Stats -->
<div class="quick-stats mt-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                Quick Stats
            </h6>
        </div>
        <div class="card-body">
            <div class="row text-center">
                <div class="col-6">
                    <div class="stat-item">
                        <h4 class="text-primary mb-0">{{ posts|length }}</h4>
                        <small class="text-muted">Posts</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-item">
                        <h4 class="text-info mb-0">{{ pages|length }}</h4>
                        <small class="text-muted">Pages</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Status -->
<div class="ai-status mt-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-brain me-2"></i>
                AI Status
            </h6>
        </div>
        <div class="card-body">
            <div id="ai-status-indicator" class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-warning me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="text-muted">Checking AI connection...</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Check AI status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAIStatus();
});

function checkAIStatus() {
    const statusIndicator = document.getElementById('ai-status-indicator');
    
    // Simple test message to check if AI is responding
    fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: 'test',
            conversation_id: null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.response) {
            statusIndicator.innerHTML = `
                <i class="fas fa-check-circle text-success me-2"></i>
                <span class="text-success">AI Online</span>
            `;
        } else {
            throw new Error('No response');
        }
    })
    .catch(error => {
        statusIndicator.innerHTML = `
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
            <span class="text-warning">AI Offline</span>
        `;
    });
}
</script>
{% endblock %}
