{% extends "base.html" %}

{% block title %}Page Not Found - Markdown CMS{% endblock %}

{% block content %}
<div class="text-center py-5">
    <div class="error-container">
        <h1 class="display-1 text-muted">404</h1>
        <h2 class="mb-4">Page Not Found</h2>
        <p class="lead mb-4">
            Sorry, the page you're looking for doesn't exist or has been moved.
        </p>
        
        <div class="mb-4">
            <a href="{{ url_for('index') }}" class="btn btn-primary me-2">
                <i class="fas fa-home me-1"></i>
                Go Home
            </a>
            <button class="btn btn-outline-info" onclick="goBack()">
                <i class="fas fa-arrow-left me-1"></i>
                Go Back
            </button>
        </div>
        
        <div class="mt-5">
            <h5>Try asking our AI host for help:</h5>
            <p class="text-muted">
                Use the chat interface on the right to ask about finding content or navigating the site.
            </p>
        </div>
    </div>
</div>

<style>
.error-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.display-1 {
    font-size: 8rem;
    font-weight: 300;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .display-1 {
        font-size: 4rem;
    }
}
</style>

<script>
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '/';
    }
}

// Auto-suggest chat message
document.addEventListener('DOMContentLoaded', function() {
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        setTimeout(() => {
            chatInput.placeholder = "I'm looking for content about...";
        }, 2000);
    }
});
</script>
{% endblock %}
