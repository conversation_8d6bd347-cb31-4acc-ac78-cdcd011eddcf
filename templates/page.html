{% extends "base.html" %}

{% block title %}{{ page.title }} - Markdown CMS{% endblock %}

{% block content %}
<article class="page-content">
    <!-- Page Header -->
    <header class="page-header mb-4">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ page.title }}</li>
                </ol>
            </nav>
            
            {% if not page.published %}
                <span class="badge bg-warning">Draft</span>
            {% endif %}
        </div>
        
        <h1 class="page-title display-4 mb-3">{{ page.title }}</h1>
        
        <div class="page-meta d-flex flex-wrap align-items-center text-muted mb-4">
            <div class="me-4">
                <i class="fas fa-calendar me-1"></i>
                <time datetime="{{ page.created_at.isoformat() }}">
                    Created {{ page.created_at.strftime('%B %d, %Y') }}
                </time>
            </div>
            
            {% if page.updated_at != page.created_at %}
                <div class="me-4">
                    <i class="fas fa-edit me-1"></i>
                    Updated {{ page.updated_at.strftime('%B %d, %Y') }}
                </div>
            {% endif %}
            
            <div class="me-4">
                <i class="fas fa-clock me-1"></i>
                {{ (page.content.split()|length / 200)|round|int }} min read
            </div>
            
            <div class="admin-actions">
                <a href="{{ url_for('admin_edit_page', slug=page.slug) }}" class="btn btn-sm btn-outline-secondary me-2">
                    <i class="fas fa-edit me-1"></i>
                    Edit
                </a>
                <button class="btn btn-sm btn-outline-info me-2" onclick="toggleMarkdownView()" id="markdown-toggle-btn">
                    <i class="fas fa-code me-1"></i>
                    View Markdown
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="toggleReadAloud()" id="read-aloud-btn">
                    <i class="fas fa-volume-up me-1"></i>
                    Read Aloud
                </button>
            </div>
        </div>
    </header>
    
    <!-- Page Content -->
    <div class="page-body">
        <div id="rendered-content">
            {{ page.content | markdown }}
        </div>
        <div id="markdown-content" style="display: none;">
            <pre class="markdown-source"><code>{{ page.content }}</code></pre>
        </div>
    </div>
    
    <!-- Page Footer -->
    <footer class="page-footer mt-5 pt-4 border-top">
        <div class="row">
            <div class="col-md-6">
                <h5>Share this page</h5>
                <div class="share-buttons">
                    <a href="https://twitter.com/intent/tweet?text={{ page.title | urlencode }}&url={{ request.url | urlencode }}" 
                       class="btn btn-outline-info btn-sm me-2" target="_blank">
                        <i class="fab fa-twitter me-1"></i>
                        Twitter
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.url | urlencode }}" 
                       class="btn btn-outline-primary btn-sm me-2" target="_blank">
                        <i class="fab fa-facebook me-1"></i>
                        Facebook
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.url | urlencode }}" 
                       class="btn btn-outline-secondary btn-sm" target="_blank">
                        <i class="fab fa-linkedin me-1"></i>
                        LinkedIn
                    </a>
                </div>
            </div>
            
            <div class="col-md-6 text-md-end">
                <h5>Questions?</h5>
                <p class="text-muted mb-2">Have questions about this page? Chat with our AI host!</p>
                <button class="btn btn-info btn-sm" onclick="startPageDiscussion()">
                    <i class="fas fa-comments me-1"></i>
                    Ask AI
                </button>
            </div>
        </div>
    </footer>
</article>
{% endblock %}

{% block sidebar %}
<!-- Table of Contents -->
<div class="toc-container mb-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                Table of Contents
            </h6>
        </div>
        <div class="card-body">
            <div id="table-of-contents">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Page Actions -->
<div class="page-actions mb-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-tools me-2"></i>
                Actions
            </h6>
        </div>
        <div class="card-body">
            <div class="d-grid gap-2">
                <a href="{{ url_for('admin_edit_page', slug=page.slug) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-edit me-1"></i>
                    Edit Page
                </a>
                <button class="btn btn-outline-success btn-sm" onclick="toggleReadAloud()" id="sidebar-read-aloud-btn">
                    <i class="fas fa-volume-up me-1"></i>
                    Read Aloud
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="copyPageLink()">
                    <i class="fas fa-link me-1"></i>
                    Copy Link
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="printPage()">
                    <i class="fas fa-print me-1"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_scripts %}
<script>
// Text-to-Speech variables
let speechSynthesis = window.speechSynthesis;
let currentUtterance = null;
let isReading = false;
let readingPosition = 0;
let pageText = '';

document.addEventListener('DOMContentLoaded', function() {
    generateTableOfContents();
    initializeTextToSpeech();
});

function generateTableOfContents() {
    const headings = document.querySelectorAll('.page-body h1, .page-body h2, .page-body h3, .page-body h4, .page-body h5, .page-body h6');
    const tocContainer = document.getElementById('table-of-contents');
    
    if (headings.length === 0) {
        tocContainer.innerHTML = '<p class="text-muted small">No headings found</p>';
        return;
    }
    
    let tocHTML = '<ul class="list-unstyled">';
    
    headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;
        
        const level = parseInt(heading.tagName.charAt(1));
        const indent = (level - 1) * 15;
        
        tocHTML += `
            <li style="margin-left: ${indent}px;">
                <a href="#${id}" class="text-decoration-none small">
                    ${heading.textContent}
                </a>
            </li>
        `;
    });
    
    tocHTML += '</ul>';
    tocContainer.innerHTML = tocHTML;
}

function startPageDiscussion() {
    const chatInput = document.getElementById('chat-input');
    const pageTitle = "{{ page.title }}";
    
    if (chatInput) {
        chatInput.value = `I have a question about the "${pageTitle}" page. `;
        chatInput.focus();
        
        // Scroll to chat
        document.querySelector('.chat-container').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }
}

function copyPageLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        btn.classList.remove('btn-outline-info');
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-info');
        }, 2000);
    });
}

function printPage() {
    window.print();
}

function toggleMarkdownView() {
    const renderedContent = document.getElementById('rendered-content');
    const markdownContent = document.getElementById('markdown-content');
    const toggleBtn = document.getElementById('markdown-toggle-btn');

    if (renderedContent.style.display === 'none') {
        // Show rendered view
        renderedContent.style.display = 'block';
        markdownContent.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-code me-1"></i>View Markdown';
        toggleBtn.classList.remove('btn-warning');
        toggleBtn.classList.add('btn-outline-info');
    } else {
        // Show markdown view
        renderedContent.style.display = 'none';
        markdownContent.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye me-1"></i>View Rendered';
        toggleBtn.classList.remove('btn-outline-info');
        toggleBtn.classList.add('btn-warning');
    }
}

// Text-to-Speech Functions
function initializeTextToSpeech() {
    // Extract clean text from the page content
    const pageContent = document.getElementById('rendered-content');
    const pageTitle = document.querySelector('.page-title').textContent;

    // Get text content and clean it up
    let textContent = pageContent.textContent || pageContent.innerText;
    textContent = textContent.replace(/\s+/g, ' ').trim();

    // Combine title and content
    pageText = `${pageTitle}. ${textContent}`;

    // Check if speech synthesis is supported
    if (!speechSynthesis) {
        console.warn('Speech synthesis not supported in this browser');
        hideReadAloudButtons();
        return;
    }

    // Load voices when they become available
    if (speechSynthesis.getVoices().length === 0) {
        speechSynthesis.addEventListener('voiceschanged', loadVoices);
    } else {
        loadVoices();
    }
}

function loadVoices() {
    const voices = speechSynthesis.getVoices();
    console.log('Available voices:', voices.map(v => `${v.name} (${v.lang})`));
}

function toggleReadAloud() {
    if (isReading) {
        stopReading();
    } else {
        startReading();
    }
}

function startReading() {
    if (!speechSynthesis || !pageText) {
        alert('Text-to-speech is not available in your browser.');
        return;
    }

    // Stop any current speech
    speechSynthesis.cancel();

    // Create new utterance
    currentUtterance = new SpeechSynthesisUtterance(pageText);

    // Configure voice settings for soft, sexy female voice
    const voices = speechSynthesis.getVoices();

    // Try to find the best female voice
    let selectedVoice = null;

    // Priority order for voice selection (soft, pleasant female voices)
    const preferredVoices = [
        'Samantha', 'Victoria', 'Allison', 'Ava', 'Susan', 'Joanna', 'Salli', 'Kimberly',
        'Microsoft Zira', 'Microsoft Hazel', 'Google UK English Female', 'Google US English',
        'Alex', 'Karen', 'Moira', 'Tessa', 'Veena', 'Fiona'
    ];

    // First, try to find preferred voices
    for (const preferredName of preferredVoices) {
        selectedVoice = voices.find(voice =>
            voice.name.includes(preferredName) &&
            voice.lang.startsWith('en')
        );
        if (selectedVoice) break;
    }

    // If no preferred voice found, look for any English female voice
    if (!selectedVoice) {
        selectedVoice = voices.find(voice =>
            voice.lang.startsWith('en') &&
            (voice.name.toLowerCase().includes('female') ||
             voice.name.toLowerCase().includes('woman') ||
             voice.name.toLowerCase().includes('zira') ||
             voice.name.toLowerCase().includes('hazel'))
        );
    }

    // Fallback to any English voice
    if (!selectedVoice) {
        selectedVoice = voices.find(voice => voice.lang.startsWith('en'));
    }

    // Apply voice if found
    if (selectedVoice) {
        currentUtterance.voice = selectedVoice;
        console.log('Selected voice:', selectedVoice.name);
    }

    // Configure speech parameters for soft, pleasant delivery
    currentUtterance.rate = 0.85;    // Slightly slower for more sensual delivery
    currentUtterance.pitch = 1.1;    // Slightly higher pitch for feminine voice
    currentUtterance.volume = 0.9;   // Soft volume

    // Event handlers
    currentUtterance.onstart = function() {
        isReading = true;
        updateReadAloudButtons(true);
        highlightReadingProgress();
    };

    currentUtterance.onend = function() {
        isReading = false;
        updateReadAloudButtons(false);
        removeReadingHighlight();
    };

    currentUtterance.onerror = function(event) {
        console.error('Speech synthesis error:', event);
        isReading = false;
        updateReadAloudButtons(false);
        alert('Sorry, there was an error with text-to-speech. Please try again.');
    };

    // Start speaking
    speechSynthesis.speak(currentUtterance);
}

function stopReading() {
    if (speechSynthesis) {
        speechSynthesis.cancel();
    }
    isReading = false;
    updateReadAloudButtons(false);
    removeReadingHighlight();
}

function updateReadAloudButtons(reading) {
    const buttons = [
        document.getElementById('read-aloud-btn'),
        document.getElementById('sidebar-read-aloud-btn')
    ];

    buttons.forEach(btn => {
        if (btn) {
            if (reading) {
                btn.innerHTML = '<i class="fas fa-stop me-1"></i>Stop Reading';
                btn.classList.remove('btn-outline-success');
                btn.classList.add('btn-danger');
            } else {
                btn.innerHTML = '<i class="fas fa-volume-up me-1"></i>Read Aloud';
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-outline-success');
            }
        }
    });
}

function highlightReadingProgress() {
    // Add a subtle animation to indicate reading is active
    const pageContent = document.getElementById('rendered-content');
    if (pageContent) {
        pageContent.style.boxShadow = '0 0 20px rgba(40, 167, 69, 0.3)';
        pageContent.style.transition = 'box-shadow 0.3s ease';
    }
}

function removeReadingHighlight() {
    const pageContent = document.getElementById('rendered-content');
    if (pageContent) {
        pageContent.style.boxShadow = '';
    }
}

function hideReadAloudButtons() {
    const buttons = [
        document.getElementById('read-aloud-btn'),
        document.getElementById('sidebar-read-aloud-btn')
    ];

    buttons.forEach(btn => {
        if (btn) {
            btn.style.display = 'none';
        }
    });
}

// Smooth scrolling for TOC links
document.addEventListener('click', function(e) {
    if (e.target.matches('#table-of-contents a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});
</script>
{% endblock %}
