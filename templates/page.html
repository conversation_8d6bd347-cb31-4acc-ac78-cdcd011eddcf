{% extends "base.html" %}

{% block title %}{{ page.title }} - Markdown CMS{% endblock %}

{% block content %}
<article class="page-content">
    <!-- Page Header -->
    <header class="page-header mb-4">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ page.title }}</li>
                </ol>
            </nav>
            
            {% if not page.published %}
                <span class="badge bg-warning">Draft</span>
            {% endif %}
        </div>
        
        <h1 class="page-title display-4 mb-3">{{ page.title }}</h1>
        
        <div class="page-meta d-flex flex-wrap align-items-center text-muted mb-4">
            <div class="me-4">
                <i class="fas fa-calendar me-1"></i>
                <time datetime="{{ page.created_at.isoformat() }}">
                    Created {{ page.created_at.strftime('%B %d, %Y') }}
                </time>
            </div>
            
            {% if page.updated_at != page.created_at %}
                <div class="me-4">
                    <i class="fas fa-edit me-1"></i>
                    Updated {{ page.updated_at.strftime('%B %d, %Y') }}
                </div>
            {% endif %}
            
            <div class="me-4">
                <i class="fas fa-clock me-1"></i>
                {{ (page.content.split()|length / 200)|round|int }} min read
            </div>
            
            <div class="admin-actions">
                <a href="{{ url_for('admin_edit_page', slug=page.slug) }}" class="btn btn-sm btn-outline-secondary me-2">
                    <i class="fas fa-edit me-1"></i>
                    Edit
                </a>
                <button class="btn btn-sm btn-outline-info" onclick="toggleMarkdownView()" id="markdown-toggle-btn">
                    <i class="fas fa-code me-1"></i>
                    View Markdown
                </button>
            </div>
        </div>
    </header>
    
    <!-- Page Content -->
    <div class="page-body">
        <div id="rendered-content">
            {{ page.content | markdown }}
        </div>
        <div id="markdown-content" style="display: none;">
            <pre class="markdown-source"><code>{{ page.content }}</code></pre>
        </div>
    </div>
    
    <!-- Page Footer -->
    <footer class="page-footer mt-5 pt-4 border-top">
        <div class="row">
            <div class="col-md-6">
                <h5>Share this page</h5>
                <div class="share-buttons">
                    <a href="https://twitter.com/intent/tweet?text={{ page.title | urlencode }}&url={{ request.url | urlencode }}" 
                       class="btn btn-outline-info btn-sm me-2" target="_blank">
                        <i class="fab fa-twitter me-1"></i>
                        Twitter
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.url | urlencode }}" 
                       class="btn btn-outline-primary btn-sm me-2" target="_blank">
                        <i class="fab fa-facebook me-1"></i>
                        Facebook
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.url | urlencode }}" 
                       class="btn btn-outline-secondary btn-sm" target="_blank">
                        <i class="fab fa-linkedin me-1"></i>
                        LinkedIn
                    </a>
                </div>
            </div>
            
            <div class="col-md-6 text-md-end">
                <h5>Questions?</h5>
                <p class="text-muted mb-2">Have questions about this page? Chat with our AI host!</p>
                <button class="btn btn-info btn-sm" onclick="startPageDiscussion()">
                    <i class="fas fa-comments me-1"></i>
                    Ask AI
                </button>
            </div>
        </div>
    </footer>
</article>
{% endblock %}

{% block sidebar %}
<!-- Table of Contents -->
<div class="toc-container mb-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                Table of Contents
            </h6>
        </div>
        <div class="card-body">
            <div id="table-of-contents">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Page Actions -->
<div class="page-actions mb-4">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-tools me-2"></i>
                Actions
            </h6>
        </div>
        <div class="card-body">
            <div class="d-grid gap-2">
                <a href="{{ url_for('admin_edit_page', slug=page.slug) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-edit me-1"></i>
                    Edit Page
                </a>
                <button class="btn btn-outline-info btn-sm" onclick="copyPageLink()">
                    <i class="fas fa-link me-1"></i>
                    Copy Link
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="printPage()">
                    <i class="fas fa-print me-1"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>

{{ super() }}
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    generateTableOfContents();
});

function generateTableOfContents() {
    const headings = document.querySelectorAll('.page-body h1, .page-body h2, .page-body h3, .page-body h4, .page-body h5, .page-body h6');
    const tocContainer = document.getElementById('table-of-contents');
    
    if (headings.length === 0) {
        tocContainer.innerHTML = '<p class="text-muted small">No headings found</p>';
        return;
    }
    
    let tocHTML = '<ul class="list-unstyled">';
    
    headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;
        
        const level = parseInt(heading.tagName.charAt(1));
        const indent = (level - 1) * 15;
        
        tocHTML += `
            <li style="margin-left: ${indent}px;">
                <a href="#${id}" class="text-decoration-none small">
                    ${heading.textContent}
                </a>
            </li>
        `;
    });
    
    tocHTML += '</ul>';
    tocContainer.innerHTML = tocHTML;
}

function startPageDiscussion() {
    const chatInput = document.getElementById('chat-input');
    const pageTitle = "{{ page.title }}";
    
    if (chatInput) {
        chatInput.value = `I have a question about the "${pageTitle}" page. `;
        chatInput.focus();
        
        // Scroll to chat
        document.querySelector('.chat-container').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }
}

function copyPageLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        btn.classList.remove('btn-outline-info');
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-info');
        }, 2000);
    });
}

function printPage() {
    window.print();
}

function toggleMarkdownView() {
    const renderedContent = document.getElementById('rendered-content');
    const markdownContent = document.getElementById('markdown-content');
    const toggleBtn = document.getElementById('markdown-toggle-btn');

    if (renderedContent.style.display === 'none') {
        // Show rendered view
        renderedContent.style.display = 'block';
        markdownContent.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-code me-1"></i>View Markdown';
        toggleBtn.classList.remove('btn-warning');
        toggleBtn.classList.add('btn-outline-info');
    } else {
        // Show markdown view
        renderedContent.style.display = 'none';
        markdownContent.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye me-1"></i>View Rendered';
        toggleBtn.classList.remove('btn-outline-info');
        toggleBtn.classList.add('btn-warning');
    }
}

// Smooth scrolling for TOC links
document.addEventListener('click', function(e) {
    if (e.target.matches('#table-of-contents a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});
</script>
{% endblock %}
