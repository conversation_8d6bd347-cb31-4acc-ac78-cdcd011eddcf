{% extends "admin/base.html" %}

{% block title %}Voice Assistant Settings - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Voice Assistant Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" id="test-voice-assistant">
            <i class="fas fa-microphone me-1"></i>
            Test Voice Assistant
        </button>
    </div>
</div>

<!-- Voice Assistant Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Voice Assistant Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div id="browser-support-status" class="status-item mb-3">
                            <h6>Browser Support</h6>
                            <div id="browser-support-indicator" class="status-indicator">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Checking...
                            </div>
                        </div>
                        <div id="microphone-status" class="status-item mb-3">
                            <h6>Microphone Permission</h6>
                            <div id="microphone-indicator" class="status-indicator">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Checking...
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div id="voice-status" class="status-item mb-3">
                            <h6>Voice Synthesis</h6>
                            <div id="voice-indicator" class="status-indicator">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Checking...
                            </div>
                        </div>
                        <div id="assistant-status" class="status-item mb-3">
                            <h6>Assistant Status</h6>
                            <div id="assistant-indicator" class="status-indicator">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Initializing...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Voice Settings -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-volume-up me-2"></i>
                    Voice Settings
                </h5>
            </div>
            <div class="card-body">
                <form id="voice-settings-form">
                    <div class="mb-3">
                        <label for="voice-select" class="form-label">Voice Selection</label>
                        <select class="form-select" id="voice-select">
                            <option value="">Loading voices...</option>
                        </select>
                        <div class="form-text">Choose your preferred voice for the assistant</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="voice-rate" class="form-label">Speech Rate: <span id="rate-value">0.85</span></label>
                        <input type="range" class="form-range" id="voice-rate" min="0.5" max="2" step="0.1" value="0.85">
                        <div class="form-text">How fast the assistant speaks</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="voice-pitch" class="form-label">Voice Pitch: <span id="pitch-value">1.1</span></label>
                        <input type="range" class="form-range" id="voice-pitch" min="0.5" max="2" step="0.1" value="1.1">
                        <div class="form-text">How high or low the voice sounds</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="voice-volume" class="form-label">Volume: <span id="volume-value">0.9</span></label>
                        <input type="range" class="form-range" id="voice-volume" min="0.1" max="1" step="0.1" value="0.9">
                        <div class="form-text">How loud the assistant speaks</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" id="test-voice">
                            <i class="fas fa-play me-1"></i>
                            Test Voice
                        </button>
                        <button type="button" class="btn btn-success" id="save-voice-settings">
                            <i class="fas fa-save me-1"></i>
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Recognition Settings
                </h5>
            </div>
            <div class="card-body">
                <form id="recognition-settings-form">
                    <div class="mb-3">
                        <label for="language-select" class="form-label">Language</label>
                        <select class="form-select" id="language-select">
                            <option value="en-US">English (US)</option>
                            <option value="en-GB">English (UK)</option>
                            <option value="en-AU">English (Australia)</option>
                            <option value="en-CA">English (Canada)</option>
                        </select>
                        <div class="form-text">Speech recognition language</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sensitivity" class="form-label">Wake Word Sensitivity: <span id="sensitivity-value">0.7</span></label>
                        <input type="range" class="form-range" id="sensitivity" min="0.3" max="1" step="0.1" value="0.7">
                        <div class="form-text">How easily "Hey Assistant" triggers the wake word</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="continuous-listening" checked>
                            <label class="form-check-label" for="continuous-listening">
                                Continuous Listening
                            </label>
                            <div class="form-text">Keep listening for wake words after commands</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="interim-results" checked>
                            <label class="form-check-label" for="interim-results">
                                Show Interim Results
                            </label>
                            <div class="form-text">Display partial recognition results</div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-success" id="save-recognition-settings">
                        <i class="fas fa-save me-1"></i>
                        Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Wake Words & Commands -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-magic me-2"></i>
                    Wake Words
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Current Wake Words</label>
                    <div id="wake-words-list" class="wake-words-container">
                        <!-- Wake words will be populated here -->
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="new-wake-word" class="form-label">Add New Wake Word</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="new-wake-word" placeholder="e.g., Hey Computer">
                        <button class="btn btn-primary" type="button" id="add-wake-word">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="form-text">Add custom phrases to wake up the assistant</div>
                </div>
                
                <button type="button" class="btn btn-info" id="test-wake-words">
                    <i class="fas fa-microphone me-1"></i>
                    Test Wake Words
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    Available Commands
                </h5>
            </div>
            <div class="card-body">
                <div class="commands-list" style="max-height: 300px; overflow-y: auto;">
                    <div class="command-category mb-3">
                        <h6 class="text-primary">Navigation</h6>
                        <ul class="list-unstyled ms-3">
                            <li><code>"Go home"</code> - Return to main site</li>
                            <li><code>"Open admin"</code> - Open admin panel</li>
                            <li><code>"Go to dashboard"</code> - Admin dashboard</li>
                        </ul>
                    </div>
                    
                    <div class="command-category mb-3">
                        <h6 class="text-success">Content</h6>
                        <ul class="list-unstyled ms-3">
                            <li><code>"Read this post"</code> - Read current content</li>
                            <li><code>"Create a new post"</code> - New post editor</li>
                            <li><code>"Show all posts"</code> - Manage posts</li>
                        </ul>
                    </div>
                    
                    <div class="command-category mb-3">
                        <h6 class="text-info">Search & Info</h6>
                        <ul class="list-unstyled ms-3">
                            <li><code>"Search for [query]"</code> - Search content</li>
                            <li><code>"What time is it?"</code> - Current time</li>
                            <li><code>"Help"</code> - Show available commands</li>
                        </ul>
                    </div>
                    
                    <div class="command-category mb-3">
                        <h6 class="text-warning">Control</h6>
                        <ul class="list-unstyled ms-3">
                            <li><code>"Stop reading"</code> - Stop text-to-speech</li>
                            <li><code>"Cancel"</code> - Cancel current action</li>
                            <li><code>"Thank you"</code> - Polite response</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Troubleshooting -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Troubleshooting & Diagnostics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Quick Actions</h6>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <button type="button" class="btn btn-outline-primary" id="reset-permissions">
                                <i class="fas fa-redo me-1"></i>
                                Reset Permissions
                            </button>
                            <button type="button" class="btn btn-outline-warning" id="clear-cache">
                                <i class="fas fa-trash me-1"></i>
                                Clear Cache
                            </button>
                            <button type="button" class="btn btn-outline-info" id="run-diagnostics">
                                <i class="fas fa-stethoscope me-1"></i>
                                Run Diagnostics
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Browser Compatibility</h6>
                        <div class="browser-compatibility">
                            <div class="d-flex justify-content-between">
                                <span>Chrome/Edge:</span>
                                <span class="text-success"><i class="fas fa-check"></i> Full Support</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Safari:</span>
                                <span class="text-warning"><i class="fas fa-exclamation-triangle"></i> Limited</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Firefox:</span>
                                <span class="text-danger"><i class="fas fa-times"></i> Basic Only</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>Diagnostic Log</h6>
                    <div id="diagnostic-log" class="diagnostic-log p-3 bg-dark text-light rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                        <div class="text-success">[INFO] Voice Assistant Settings page loaded</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/voice-assistant-settings.js') }}"></script>
{% endblock %}
