{% extends "admin/base.html" %}

{% block title %}{{ 'Edit' if action == 'edit' else 'New' }} Post - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ 'Edit' if action == 'edit' else 'New' }} Post</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin_posts') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Back to Posts
        </a>
    </div>
</div>

<form id="post-form" method="POST" action="{{ url_for('api_posts') if action == 'create' else url_for('api_post', slug=post.slug) }}">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Content</h5>
                </div>
                <div class="card-body">
                    <!-- Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">Title *</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="{{ post.title if post else '' }}" required>
                    </div>
                    
                    <!-- Slug -->
                    <div class="mb-3">
                        <label for="slug" class="form-label">Slug</label>
                        <div class="input-group">
                            <span class="input-group-text">/post/</span>
                            <input type="text" class="form-control" id="slug" name="slug" 
                                   value="{{ post.slug if post else '' }}">
                        </div>
                        <div class="form-text">Leave empty to auto-generate from title</div>
                    </div>
                    
                    <!-- Content Editor -->
                    <div class="mb-3">
                        <label for="content" class="form-label">Content *</label>
                        <div class="editor-container">
                            <div class="editor-toolbar mb-2">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('**', '**')" title="Bold">
                                        <i class="fas fa-bold"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('*', '*')" title="Italic">
                                        <i class="fas fa-italic"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('`', '`')" title="Code">
                                        <i class="fas fa-code"></i>
                                    </button>
                                </div>
                                <div class="btn-group btn-group-sm ms-2" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('# ', '')" title="Heading 1">
                                        H1
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('## ', '')" title="Heading 2">
                                        H2
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('### ', '')" title="Heading 3">
                                        H3
                                    </button>
                                </div>
                                <div class="btn-group btn-group-sm ms-2" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('- ', '')" title="List">
                                        <i class="fas fa-list-ul"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('1. ', '')" title="Numbered List">
                                        <i class="fas fa-list-ol"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertMarkdown('> ', '')" title="Quote">
                                        <i class="fas fa-quote-right"></i>
                                    </button>
                                </div>
                                <div class="btn-group btn-group-sm ms-2" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertLink()" title="Link">
                                        <i class="fas fa-link"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertImage()" title="Image">
                                        <i class="fas fa-image"></i>
                                    </button>
                                </div>
                                <div class="btn-group btn-group-sm ms-auto" role="group">
                                    <button type="button" class="btn btn-outline-info" onclick="togglePreview()" id="preview-btn">
                                        <i class="fas fa-eye"></i> Preview
                                    </button>
                                </div>
                            </div>
                            
                            <div class="editor-content">
                                <textarea class="form-control" id="content" name="content" rows="20" required>{{ post.content if post else '' }}</textarea>
                                <div id="preview-content" class="preview-content" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Publish</h6>
                </div>
                <div class="card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="published" name="published" 
                               {{ 'checked' if post and post.published else '' }}>
                        <label class="form-check-label" for="published">
                            Published
                        </label>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {{ 'Update' if action == 'edit' else 'Create' }} Post
                        </button>
                        {% if action == 'edit' %}
                            <a href="{{ url_for('view_post', slug=post.slug) }}" class="btn btn-outline-info" target="_blank">
                                <i class="fas fa-eye me-1"></i>
                                View Post
                            </a>
                        {% endif %}
                        <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                            <i class="fas fa-file-alt me-1"></i>
                            Save as Draft
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Tags -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Tags</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="tags-input" 
                               placeholder="Add tags (comma separated)">
                    </div>
                    <div id="tags-container">
                        {% if post and post.tags %}
                            {% for tag in post.tags %}
                                <span class="badge bg-secondary me-1 mb-1">
                                    #{{ tag }}
                                    <button type="button" class="btn-close btn-close-white ms-1" 
                                            onclick="removeTag('{{ tag }}')"></button>
                                </span>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <input type="hidden" id="tags" name="tags" 
                           value="{{ post.tags|join(',') if post and post.tags else '' }}">
                </div>
            </div>
            
            <!-- File Upload -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Media</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="file" class="form-control" id="file-upload" 
                               accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.md">
                    </div>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="uploadFile()">
                            <i class="fas fa-upload me-1"></i>
                            Upload File
                        </button>
                    </div>
                    <div id="upload-progress" class="mt-2" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Post Stats -->
            {% if post %}
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <h6 class="text-primary mb-0">{{ (post.content.split()|length / 200)|round|int }}</h6>
                                <small class="text-muted">Min Read</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h6 class="text-info mb-0">{{ post.content.split()|length }}</h6>
                                <small class="text-muted">Words</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <small class="text-muted">
                        Created: {{ post.created_at.strftime('%b %d, %Y at %I:%M %p') }}<br>
                        Updated: {{ post.updated_at.strftime('%b %d, %Y at %I:%M %p') }}
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</form>
{% endblock %}

{% block extra_scripts %}
<script>
let currentTags = {{ post.tags|tojson if post and post.tags else '[]' }};
let isPreviewMode = false;

// Auto-generate slug from title
document.getElementById('title').addEventListener('input', function() {
    const slugField = document.getElementById('slug');
    if (!slugField.value || slugField.dataset.autoGenerated !== 'false') {
        const slug = this.value.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[-\s]+/g, '-')
            .trim('-');
        slugField.value = slug;
        slugField.dataset.autoGenerated = 'true';
    }
});

document.getElementById('slug').addEventListener('input', function() {
    this.dataset.autoGenerated = 'false';
});

// Tags functionality
document.getElementById('tags-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' || e.key === ',') {
        e.preventDefault();
        addTag(this.value.trim());
        this.value = '';
    }
});

function addTag(tag) {
    if (tag && !currentTags.includes(tag)) {
        currentTags.push(tag);
        updateTagsDisplay();
        updateTagsInput();
    }
}

function removeTag(tag) {
    currentTags = currentTags.filter(t => t !== tag);
    updateTagsDisplay();
    updateTagsInput();
}

function updateTagsDisplay() {
    const container = document.getElementById('tags-container');
    container.innerHTML = currentTags.map(tag => `
        <span class="badge bg-secondary me-1 mb-1">
            #${tag}
            <button type="button" class="btn-close btn-close-white ms-1" 
                    onclick="removeTag('${tag}')"></button>
        </span>
    `).join('');
}

function updateTagsInput() {
    document.getElementById('tags').value = currentTags.join(',');
}

// Markdown editor functions
function insertMarkdown(before, after) {
    const textarea = document.getElementById('content');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    
    const newText = before + selectedText + after;
    textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
    
    // Set cursor position
    const newCursorPos = start + before.length + selectedText.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();
}

function insertLink() {
    const url = prompt('Enter URL:');
    if (url) {
        const text = prompt('Enter link text:', url);
        insertMarkdown(`[${text || url}](`, `${url})`);
    }
}

function insertImage() {
    const url = prompt('Enter image URL:');
    if (url) {
        const alt = prompt('Enter alt text:', 'Image');
        insertMarkdown(`![${alt}](`, `${url})`);
    }
}

function togglePreview() {
    const textarea = document.getElementById('content');
    const preview = document.getElementById('preview-content');
    const btn = document.getElementById('preview-btn');
    
    isPreviewMode = !isPreviewMode;
    
    if (isPreviewMode) {
        // Show preview
        textarea.style.display = 'none';
        preview.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-edit"></i> Edit';
        
        // Convert markdown to HTML (simplified)
        preview.innerHTML = convertMarkdownToHTML(textarea.value);
    } else {
        // Show editor
        textarea.style.display = 'block';
        preview.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-eye"></i> Preview';
    }
}

function convertMarkdownToHTML(markdown) {
    // Simple markdown conversion (in a real app, use a proper library)
    return markdown
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gim, '<em>$1</em>')
        .replace(/`(.*)`/gim, '<code>$1</code>')
        .replace(/\n/gim, '<br>');
}

// File upload
function uploadFile() {
    const fileInput = document.getElementById('file-upload');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a file first.');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    const progressContainer = document.getElementById('upload-progress');
    const progressBar = progressContainer.querySelector('.progress-bar');
    
    progressContainer.style.display = 'block';
    progressBar.style.width = '0%';
    
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.url) {
            // Insert image/link into content
            const textarea = document.getElementById('content');
            const isImage = file.type.startsWith('image/');
            const markdown = isImage ? 
                `![${file.name}](${data.url})` : 
                `[${file.name}](${data.url})`;
            
            textarea.value += '\n' + markdown;
            
            // Show success
            progressBar.style.width = '100%';
            progressBar.classList.add('bg-success');
            
            setTimeout(() => {
                progressContainer.style.display = 'none';
                progressBar.classList.remove('bg-success');
                fileInput.value = '';
            }, 2000);
        } else {
            throw new Error(data.error || 'Upload failed');
        }
    })
    .catch(error => {
        progressBar.classList.add('bg-danger');
        alert('Upload failed: ' + error.message);
        setTimeout(() => {
            progressContainer.style.display = 'none';
            progressBar.classList.remove('bg-danger');
        }, 2000);
    });
}

// Form submission
document.getElementById('post-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        title: formData.get('title'),
        content: formData.get('content'),
        slug: formData.get('slug'),
        published: formData.has('published'),
        tags: currentTags
    };
    
    const method = '{{ "PUT" if action == "edit" else "POST" }}';
    const url = this.action;
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.slug) {
            window.location.href = `/admin/posts/edit/${data.slug}`;
        } else {
            alert('Error: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
});

function saveDraft() {
    document.getElementById('published').checked = false;
    document.getElementById('post-form').dispatchEvent(new Event('submit'));
}
</script>
{% endblock %}
