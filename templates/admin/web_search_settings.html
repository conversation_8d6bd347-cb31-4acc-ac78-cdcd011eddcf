{% extends "admin/base.html" %}

{% block title %}Web Search Settings - Admin{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>
                        Web Search Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="web-search-settings-form" method="POST" action="{{ url_for('api_web_search_config') }}">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_web_search" name="enable_web_search" {% if config.enable_web_search %}checked{% endif %}>
                                <label class="form-check-label" for="enable_web_search">Enable Web Search</label>
                            </div>
                            <small class="text-muted">When enabled, the AI can search the web for information it doesn't know.</small>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="search_max_results" class="form-label">Maximum Results</label>
                                <input type="number" class="form-control" id="search_max_results" name="search_max_results" value="{{ config.search_max_results|default(5) }}" min="1" max="10">
                                <small class="text-muted">Number of search results to retrieve (1-10)</small>
                            </div>
                            <div class="col-md-6">
                                <label for="search_timeout" class="form-label">Search Timeout (seconds)</label>
                                <input type="number" class="form-control" id="search_timeout" name="search_timeout" value="{{ config.search_timeout|default(10) }}" min="1" max="30">
                                <small class="text-muted">Maximum time to wait for search results</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="search_confidence_threshold" class="form-label">Confidence Threshold</label>
                            <input type="range" class="form-range" id="search_confidence_threshold" name="search_confidence_threshold" min="0" max="1" step="0.1" value="{{ config.search_confidence_threshold|default(0.6) }}">
                            <div class="d-flex justify-content-between">
                                <small>Low (search more often)</small>
                                <small id="threshold-value">{{ config.search_confidence_threshold|default(0.6) }}</small>
                                <small>High (search less often)</small>
                            </div>
                            <small class="text-muted">Threshold for when the AI should search the web</small>
                        </div>

                        <hr>
                        <h5>Search API Configuration</h5>
                        <p class="text-muted">Configure at least one search API for best results.</p>

                        <div class="mb-3">
                            <label for="bing_api_key" class="form-label">Bing Search API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="bing_api_key" name="bing_api_key" value="{{ config.bing_api_key|default('') }}">
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="bing_api_key">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">Get a key from <a href="https://portal.azure.com/#create/microsoft.bingsearch" target="_blank">Azure Portal</a></small>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="google_api_key" class="form-label">Google Search API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="google_api_key" name="google_api_key" value="{{ config.google_api_key|default('') }}">
                                    <button class="btn btn-outline-secondary toggle-password" type="button" data-target="google_api_key">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="google_cx" class="form-label">Google Search Engine ID (CX)</label>
                                <input type="text" class="form-control" id="google_cx" name="google_cx" value="{{ config.google_cx|default('') }}">
                            </div>
                            <div class="col-12 mt-1">
                                <small class="text-muted">Get from <a href="https://programmablesearchengine.google.com/" target="_blank">Programmable Search Engine</a></small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="serp_api_key" class="form-label">SerpAPI Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="serp_api_key" name="serp_api_key" value="{{ config.serp_api_key|default('') }}">
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="serp_api_key">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">Get a key from <a href="https://serpapi.com/" target="_blank">SerpAPI</a></small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="use_duckduckgo_fallback" name="use_duckduckgo_fallback" {% if config.use_duckduckgo_fallback %}checked{% endif %}>
                                <label class="form-check-label" for="use_duckduckgo_fallback">Use DuckDuckGo as fallback</label>
                            </div>
                            <small class="text-muted">Use DuckDuckGo when no API keys are available (limited functionality)</small>
                        </div>

                        <hr>
                        <h5>Search Logs</h5>
                        
                        <div class="mb-3">
                            <label for="search_log_dir" class="form-label">Log Directory</label>
                            <input type="text" class="form-control" id="search_log_dir" name="search_log_dir" value="{{ config.search_log_dir|default('data/search_logs') }}">
                            <small class="text-muted">Directory where search logs are stored</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable_search_logging" name="enable_search_logging" {% if config.enable_search_logging %}checked{% endif %}>
                                <label class="form-check-label" for="enable_search_logging">Enable Search Logging</label>
                            </div>
                            <small class="text-muted">Log all web searches for monitoring and debugging</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Save Settings
                            </button>
                            <button type="button" id="test-search-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-vial me-2"></i>
                                Test Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Search Statistics Card -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Search Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="total-searches">{{ stats.total_searches|default(0) }}</h3>
                                    <p class="mb-0">Total Searches</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="successful-searches">{{ stats.successful_searches|default(0) }}</h3>
                                    <p class="mb-0">Successful</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="avg-results">{{ stats.avg_results|default('0.0') }}</h3>
                                    <p class="mb-0">Avg Results</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="avg-response-time">{{ stats.avg_response_time|default('0.0') }}s</h3>
                                    <p class="mb-0">Avg Response Time</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Recent Searches</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>Query</th>
                                        <th>Time</th>
                                        <th>Results</th>
                                        <th>Response Time</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-searches-table">
                                    {% if recent_searches %}
                                        {% for search in recent_searches %}
                                        <tr>
                                            <td>{{ search.query }}</td>
                                            <td>{{ search.timestamp|datetime }}</td>
                                            <td>{{ search.result_count }}</td>
                                            <td>{{ search.response_time }}s</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center">No recent searches</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Search Modal -->
<div class="modal fade" id="testSearchModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Web Search</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="test-query" class="form-label">Search Query</label>
                    <input type="text" class="form-control" id="test-query" placeholder="Enter a search query...">
                </div>
                
                <div id="search-results-container" class="d-none">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Search Results</h6>
                        <span class="badge bg-info" id="search-time"></span>
                    </div>
                    
                    <div class="search-results border rounded p-3 bg-light">
                        <div id="search-results"></div>
                    </div>
                </div>
                
                <div id="search-error" class="alert alert-danger d-none"></div>
                <div id="search-loading" class="text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Searching...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="run-test-search">
                    <i class="fas fa-search me-2"></i>
                    Search
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update threshold value display
        const thresholdInput = document.getElementById('search_confidence_threshold');
        const thresholdValue = document.getElementById('threshold-value');
        
        thresholdInput.addEventListener('input', function() {
            thresholdValue.textContent = this.value;
        });
        
        // Toggle password visibility
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                
                if (input.type === 'password') {
                    input.type = 'text';
                    this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    input.type = 'password';
                    this.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });
        
        // Form submission
        const form = document.getElementById('web-search-settings-form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = {};
            
            for (const [key, value] of formData.entries()) {
                if (key === 'enable_web_search' || key === 'use_duckduckgo_fallback' || key === 'enable_search_logging') {
                    data[key] = true;  // Checkbox is present in formData only when checked
                } else if (key === 'search_confidence_threshold' || key === 'search_max_results' || key === 'search_timeout') {
                    data[key] = parseFloat(value);
                } else {
                    data[key] = value;
                }
            }
            
            // Add unchecked checkboxes (not included in formData)
            if (!formData.has('enable_web_search')) data.enable_web_search = false;
            if (!formData.has('use_duckduckgo_fallback')) data.use_duckduckgo_fallback = false;
            if (!formData.has('enable_search_logging')) data.enable_search_logging = false;
            
            fetch('{{ url_for("api_web_search_config") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Settings saved successfully', 'success');
                } else {
                    showNotification('Error saving settings: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                showNotification('Error saving settings: ' + error, 'danger');
            });
        });
        
        // Test search button
        document.getElementById('test-search-btn').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('testSearchModal'));
            modal.show();
        });
        
        // Run test search
        document.getElementById('run-test-search').addEventListener('click', function() {
            const query = document.getElementById('test-query').value.trim();
            
            if (!query) {
                showNotification('Please enter a search query', 'warning');
                return;
            }
            
            // Show loading
            document.getElementById('search-loading').classList.remove('d-none');
            document.getElementById('search-results-container').classList.add('d-none');
            document.getElementById('search-error').classList.add('d-none');
            
            fetch('{{ url_for("api_test_web_search") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                // Hide loading
                document.getElementById('search-loading').classList.add('d-none');
                
                if (data.error) {
                    document.getElementById('search-error').textContent = data.error;
                    document.getElementById('search-error').classList.remove('d-none');
                    return;
                }
                
                // Show results
                document.getElementById('search-results-container').classList.remove('d-none');
                document.getElementById('search-time').textContent = `${data.response_time.toFixed(2)}s`;
                
                const resultsContainer = document.getElementById('search-results');
                resultsContainer.innerHTML = '';
                
                if (data.results.length === 0) {
                    resultsContainer.innerHTML = '<p class="text-muted">No results found</p>';
                    return;
                }
                
                // Display results
                data.results.forEach((result, index) => {
                    const resultElement = document.createElement('div');
                    resultElement.className = 'mb-3';
                    resultElement.innerHTML = `
                        <h6 class="mb-1">${index + 1}. <a href="${result.link}" target="_blank">${result.title}</a></h6>
                        <div class="small text-muted mb-1">${result.link}</div>
                        <p class="mb-0">${result.snippet}</p>
                    `;
                    resultsContainer.appendChild(resultElement);
                    
                    if (index < data.results.length - 1) {
                        const divider = document.createElement('hr');
                        divider.className = 'my-2';
                        resultsContainer.appendChild(divider);
                    }
                });
            })
            .catch(error => {
                document.getElementById('search-loading').classList.add('d-none');
                document.getElementById('search-error').textContent = 'Error performing search: ' + error;
                document.getElementById('search-error').classList.remove('d-none');
            });
        });
    });
    
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
</script>
{% endblock %}