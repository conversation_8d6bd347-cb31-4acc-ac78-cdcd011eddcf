{% extends "admin/base.html" %}

{% block title %}Dashboard - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin_new_post') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>
                New Post
            </a>
            <a href="{{ url_for('admin_new_page') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-plus me-1"></i>
                New Page
            </a>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Posts
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ posts|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Published Posts
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ posts|selectattr("published")|list|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Pages
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pages|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Draft Posts
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ posts|rejectattr("published")|list|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Content -->
<div class="row">
    <!-- Recent Posts -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Posts</h6>
                <a href="{{ url_for('admin_posts') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if posts %}
                    <div class="list-group list-group-flush">
                        {% for post in posts[:5] %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">
                                        <a href="{{ url_for('admin_edit_post', slug=post.slug) }}" class="text-decoration-none">
                                            {{ post.title }}
                                        </a>
                                    </div>
                                    <small class="text-muted">{{ post.created_at.strftime('%b %d, %Y') }}</small>
                                </div>
                                <span class="badge bg-{{ 'success' if post.published else 'warning' }} rounded-pill">
                                    {{ 'Published' if post.published else 'Draft' }}
                                </span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No posts yet</p>
                        <a href="{{ url_for('admin_new_post') }}" class="btn btn-primary">Create First Post</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Pages -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Pages</h6>
                <a href="{{ url_for('admin_pages') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if pages %}
                    <div class="list-group list-group-flush">
                        {% for page in pages[:5] %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">
                                        <a href="{{ url_for('admin_edit_page', slug=page.slug) }}" class="text-decoration-none">
                                            {{ page.title }}
                                        </a>
                                    </div>
                                    <small class="text-muted">{{ page.created_at.strftime('%b %d, %Y') }}</small>
                                </div>
                                <span class="badge bg-{{ 'success' if page.published else 'warning' }} rounded-pill">
                                    {{ 'Published' if page.published else 'Draft' }}
                                </span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No pages yet</p>
                        <a href="{{ url_for('admin_new_page') }}" class="btn btn-primary">Create First Page</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-plus fa-2x text-primary mb-3"></i>
                                <h6 class="card-title">Create Content</h6>
                                <div class="d-grid gap-2">
                                    <a href="{{ url_for('admin_new_post') }}" class="btn btn-primary btn-sm">New Post</a>
                                    <a href="{{ url_for('admin_new_page') }}" class="btn btn-outline-primary btn-sm">New Page</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-eye fa-2x text-success mb-3"></i>
                                <h6 class="card-title">View Site</h6>
                                <div class="d-grid gap-2">
                                    <a href="{{ url_for('index') }}" target="_blank" class="btn btn-success btn-sm">Homepage</a>
                                    <a href="{{ url_for('admin_posts') }}" class="btn btn-outline-success btn-sm">Manage Posts</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-upload fa-2x text-info mb-3"></i>
                                <h6 class="card-title">Upload Files</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-info btn-sm" onclick="triggerFileUpload()">Upload Image</button>
                                    <button class="btn btn-outline-info btn-sm" onclick="showUploadModal()">Manage Files</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-robot fa-2x text-warning mb-3"></i>
                                <h6 class="card-title">AI Status</h6>
                                <div id="ai-status-dashboard" class="d-grid gap-2">
                                    <div class="spinner-border spinner-border-sm text-warning" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <small class="text-muted">Checking...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden file input for uploads -->
<input type="file" id="fileUpload" style="display: none;" accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.md">
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    checkAIStatus();
    setupFileUpload();
});

function checkAIStatus() {
    const statusContainer = document.getElementById('ai-status-dashboard');
    
    fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: 'test',
            conversation_id: null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.response) {
            statusContainer.innerHTML = `
                <button class="btn btn-success btn-sm">
                    <i class="fas fa-check-circle"></i>
                </button>
                <small class="text-success">Online</small>
            `;
        } else {
            throw new Error('No response');
        }
    })
    .catch(error => {
        statusContainer.innerHTML = `
            <button class="btn btn-warning btn-sm">
                <i class="fas fa-exclamation-triangle"></i>
            </button>
            <small class="text-warning">Offline</small>
        `;
    });
}

function triggerFileUpload() {
    document.getElementById('fileUpload').click();
}

function setupFileUpload() {
    const fileInput = document.getElementById('fileUpload');
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            uploadFile(file);
        }
    });
}

function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    // Show loading state
    const uploadBtn = document.querySelector('button[onclick="triggerFileUpload()"]');
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
    uploadBtn.disabled = true;
    
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.url) {
            // Show success message
            uploadBtn.innerHTML = '<i class="fas fa-check"></i> Uploaded!';
            uploadBtn.classList.remove('btn-info');
            uploadBtn.classList.add('btn-success');
            
            // Copy URL to clipboard
            navigator.clipboard.writeText(data.url);
            
            setTimeout(() => {
                uploadBtn.innerHTML = originalText;
                uploadBtn.classList.remove('btn-success');
                uploadBtn.classList.add('btn-info');
                uploadBtn.disabled = false;
            }, 3000);
        } else {
            throw new Error(data.error || 'Upload failed');
        }
    })
    .catch(error => {
        uploadBtn.innerHTML = '<i class="fas fa-times"></i> Failed';
        uploadBtn.classList.remove('btn-info');
        uploadBtn.classList.add('btn-danger');
        
        setTimeout(() => {
            uploadBtn.innerHTML = originalText;
            uploadBtn.classList.remove('btn-danger');
            uploadBtn.classList.add('btn-info');
            uploadBtn.disabled = false;
        }, 3000);
        
        console.error('Upload error:', error);
    });
}

function showUploadModal() {
    // This would open a file management modal
    alert('File management modal would open here. For now, use the upload button to add files.');
}
</script>
{% endblock %}
