{% extends "admin/base.html" %}

{% block title %}AI Settings - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">AI Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-outline-primary" onclick="refreshModels()">
            <i class="fas fa-sync-alt me-1"></i>
            Refresh Models
        </button>
    </div>
</div>

<!-- AI Status Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>
                    AI Service Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div id="ai-status-display" class="d-flex align-items-center mb-3">
                            <div class="spinner-border spinner-border-sm text-warning me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="text-muted">Checking AI service...</span>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Service URL:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="service-url" value="http://localhost:1234" readonly>
                                <button class="btn btn-outline-secondary" onclick="testConnection()">
                                    <i class="fas fa-plug"></i> Test
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="ai-stats">
                            <h6>Quick Stats</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h5 class="text-primary mb-0" id="models-count">-</h5>
                                        <small class="text-muted">Models</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h5 class="text-info mb-0" id="conversations-count">-</h5>
                                        <small class="text-muted">Conversations</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h5 class="text-success mb-0" id="uptime-display">-</h5>
                                        <small class="text-muted">Uptime</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Model Management -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-brain me-2"></i>
                    Available Models
                </h5>
            </div>
            <div class="card-body">
                <div id="models-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading models...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading available models...</p>
                </div>
                
                <div id="models-list" style="display: none;">
                    <!-- Models will be populated here -->
                </div>
                
                <div id="models-error" style="display: none;" class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Unable to load models. Make sure your AI service is running.
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Current Model Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    Current Model
                </h6>
            </div>
            <div class="card-body">
                <div id="current-model-info">
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm text-muted" role="status"></div>
                        <p class="mt-2 text-muted small">Loading...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI Configuration -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Configuration
                </h6>
            </div>
            <div class="card-body">
                <form id="ai-config-form">
                    <div class="mb-3">
                        <label for="temperature" class="form-label">Temperature</label>
                        <input type="range" class="form-range" id="temperature" min="0" max="2" step="0.1" value="0.7">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">Conservative</small>
                            <small class="text-muted" id="temperature-value">0.7</small>
                            <small class="text-muted">Creative</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max-tokens" class="form-label">Max Tokens</label>
                        <input type="number" class="form-control" id="max-tokens" value="2048" min="100" max="8192">
                        <div class="form-text">Maximum response length</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Test Chat -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments me-2"></i>
                    Test Chat
                </h5>
            </div>
            <div class="card-body">
                <div id="test-chat-messages" class="border rounded p-3 mb-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                    <div class="text-muted text-center py-4">
                        <i class="fas fa-comment-dots fa-2x mb-2"></i>
                        <p>Test your AI configuration here</p>
                    </div>
                </div>
                
                <div class="input-group">
                    <input type="text" class="form-control" id="test-message" placeholder="Type a test message...">
                    <button class="btn btn-primary" onclick="sendTestMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentModels = [];
let currentModel = null;

document.addEventListener('DOMContentLoaded', function() {
    loadAIStatus();
    loadModels();
    setupConfigForm();
});

function loadAIStatus() {
    fetch('/api/models')
        .then(response => response.json())
        .then(data => {
            updateAIStatus(data);
        })
        .catch(error => {
            console.error('Error loading AI status:', error);
            updateAIStatusError();
        });
}

function updateAIStatus(data) {
    const statusDisplay = document.getElementById('ai-status-display');
    const modelsCount = document.getElementById('models-count');
    
    if (data.is_available) {
        statusDisplay.innerHTML = `
            <i class="fas fa-check-circle text-success me-2"></i>
            <span class="text-success">AI Service Online</span>
        `;
        modelsCount.textContent = data.models.length;
    } else {
        statusDisplay.innerHTML = `
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
            <span class="text-warning">AI Service Offline</span>
        `;
        modelsCount.textContent = '0';
    }
    
    currentModel = data.current_model;
    updateCurrentModelInfo();
}

function updateAIStatusError() {
    const statusDisplay = document.getElementById('ai-status-display');
    statusDisplay.innerHTML = `
        <i class="fas fa-times-circle text-danger me-2"></i>
        <span class="text-danger">Connection Error</span>
    `;
}

function loadModels() {
    const loadingDiv = document.getElementById('models-loading');
    const listDiv = document.getElementById('models-list');
    const errorDiv = document.getElementById('models-error');
    
    loadingDiv.style.display = 'block';
    listDiv.style.display = 'none';
    errorDiv.style.display = 'none';
    
    fetch('/api/models')
        .then(response => response.json())
        .then(data => {
            currentModels = data.models;
            currentModel = data.current_model;
            
            loadingDiv.style.display = 'none';
            
            if (data.models.length > 0) {
                displayModels(data.models, data.current_model);
                listDiv.style.display = 'block';
            } else {
                errorDiv.style.display = 'block';
            }
            
            updateCurrentModelInfo();
        })
        .catch(error => {
            console.error('Error loading models:', error);
            loadingDiv.style.display = 'none';
            errorDiv.style.display = 'block';
        });
}

function displayModels(models, currentModel) {
    const listDiv = document.getElementById('models-list');
    
    const modelsHTML = models.map(model => `
        <div class="model-item border rounded p-3 mb-2 ${model === currentModel ? 'border-primary bg-light' : ''}">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${model}</h6>
                    ${model === currentModel ? '<span class="badge bg-primary">Current</span>' : ''}
                </div>
                <div>
                    ${model !== currentModel ? `
                        <button class="btn btn-sm btn-outline-primary" onclick="switchModel('${model}')">
                            <i class="fas fa-exchange-alt me-1"></i>
                            Switch
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-primary" disabled>
                            <i class="fas fa-check me-1"></i>
                            Active
                        </button>
                    `}
                </div>
            </div>
        </div>
    `).join('');
    
    listDiv.innerHTML = modelsHTML;
}

function updateCurrentModelInfo() {
    const infoDiv = document.getElementById('current-model-info');
    
    if (currentModel) {
        infoDiv.innerHTML = `
            <div class="text-center">
                <i class="fas fa-brain fa-2x text-primary mb-2"></i>
                <h6>${currentModel}</h6>
                <small class="text-muted">Active Model</small>
            </div>
        `;
    } else {
        infoDiv.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-question-circle fa-2x mb-2"></i>
                <p>No model selected</p>
            </div>
        `;
    }
}

function switchModel(modelName) {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Switching...';
    
    fetch('/api/models/switch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ model_name: modelName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.current_model) {
            currentModel = data.current_model;
            showNotification('Model switched successfully!', 'success');
            loadModels(); // Refresh the display
        } else {
            throw new Error(data.error || 'Failed to switch model');
        }
    })
    .catch(error => {
        console.error('Error switching model:', error);
        showNotification('Failed to switch model: ' + error.message, 'danger');
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function refreshModels() {
    loadModels();
    showNotification('Refreshing models...', 'info');
}

function testConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    
    fetch('/api/models')
        .then(response => response.json())
        .then(data => {
            if (data.is_available) {
                showNotification('Connection successful!', 'success');
            } else {
                showNotification('Service is offline', 'warning');
            }
        })
        .catch(error => {
            showNotification('Connection failed', 'danger');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

function setupConfigForm() {
    const temperatureSlider = document.getElementById('temperature');
    const temperatureValue = document.getElementById('temperature-value');
    
    temperatureSlider.addEventListener('input', function() {
        temperatureValue.textContent = this.value;
    });
    
    document.getElementById('ai-config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        showNotification('Configuration saved!', 'success');
    });
}

function sendTestMessage() {
    const input = document.getElementById('test-message');
    const message = input.value.trim();
    
    if (!message) return;
    
    const messagesDiv = document.getElementById('test-chat-messages');
    
    // Add user message
    messagesDiv.innerHTML += `
        <div class="mb-2">
            <strong>You:</strong> ${message}
        </div>
    `;
    
    // Add loading indicator
    messagesDiv.innerHTML += `
        <div class="mb-2" id="ai-loading">
            <strong>AI:</strong> <i class="fas fa-spinner fa-spin"></i> Thinking...
        </div>
    `;
    
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
    input.value = '';
    
    // Send to AI
    fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message: message, conversation_id: null })
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('ai-loading').remove();
        messagesDiv.innerHTML += `
            <div class="mb-2">
                <strong>AI:</strong> ${data.response}
            </div>
        `;
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    })
    .catch(error => {
        document.getElementById('ai-loading').remove();
        messagesDiv.innerHTML += `
            <div class="mb-2 text-danger">
                <strong>Error:</strong> Failed to get AI response
            </div>
        `;
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    });
}

// Allow Enter key to send test messages
document.getElementById('test-message').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendTestMessage();
    }
});

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
