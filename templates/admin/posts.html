{% extends "admin/base.html" %}

{% block title %}Posts - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">Posts</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin_new_post') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            New Post
        </a>
    </div>
</div>

<!-- Posts Table -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">All Posts ({{ posts|length }})</h5>
            </div>
            <div class="col-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Search posts..." id="search-posts">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    {% if posts %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Title</th>
                        <th>Status</th>
                        <th>Tags</th>
                        <th>Created</th>
                        <th>Updated</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for post in posts %}
                        <tr>
                            <td>
                                <div class="d-flex flex-column">
                                    <strong>{{ post.title }}</strong>
                                    <small class="text-muted">{{ post.excerpt[:100] }}{% if post.excerpt|length > 100 %}...{% endif %}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if post.published else 'warning' }}">
                                    {{ 'Published' if post.published else 'Draft' }}
                                </span>
                            </td>
                            <td>
                                {% if post.tags %}
                                    {% for tag in post.tags[:3] %}
                                        <span class="badge bg-secondary me-1">#{{ tag }}</span>
                                    {% endfor %}
                                    {% if post.tags|length > 3 %}
                                        <span class="text-muted">+{{ post.tags|length - 3 }} more</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">No tags</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ post.created_at.strftime('%b %d, %Y') }}</small>
                            </td>
                            <td>
                                <small>{{ post.updated_at.strftime('%b %d, %Y') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('view_post', slug=post.slug) }}" 
                                       class="btn btn-outline-info" target="_blank" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('admin_edit_post', slug=post.slug) }}" 
                                       class="btn btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-outline-danger" 
                                            onclick="deletePost('{{ post.slug }}')" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="card-body text-center py-5">
            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No posts yet</h4>
            <p class="text-muted">Create your first post to get started.</p>
            <a href="{{ url_for('admin_new_post') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create First Post
            </a>
        </div>
    {% endif %}
</div>

<!-- Bulk Actions -->
{% if posts %}
<div class="mt-3">
    <div class="card">
        <div class="card-body">
            <h6 class="card-title">Bulk Actions</h6>
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="select-all-posts">
                        <label class="form-check-label" for="select-all-posts">
                            Select all posts
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="bulkPublish()">
                            <i class="fas fa-check me-1"></i>
                            Publish Selected
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="bulkUnpublish()">
                            <i class="fas fa-eye-slash me-1"></i>
                            Unpublish Selected
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="bulkDelete()">
                            <i class="fas fa-trash me-1"></i>
                            Delete Selected
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// Search functionality
document.getElementById('search-posts').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const title = row.querySelector('td:first-child strong').textContent.toLowerCase();
        const excerpt = row.querySelector('td:first-child small').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || excerpt.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Delete post
function deletePost(slug) {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        fetch(`/api/posts/${slug}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                location.reload();
            } else {
                alert('Error deleting post: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Error deleting post: ' + error.message);
        });
    }
}

// Bulk actions
document.getElementById('select-all-posts').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function getSelectedPosts() {
    const selected = [];
    document.querySelectorAll('tbody input[type="checkbox"]:checked').forEach(cb => {
        selected.push(cb.value);
    });
    return selected;
}

function bulkPublish() {
    const selected = getSelectedPosts();
    if (selected.length === 0) {
        alert('Please select posts to publish.');
        return;
    }
    
    if (confirm(`Publish ${selected.length} selected posts?`)) {
        // Implementation would go here
        alert('Bulk publish functionality would be implemented here.');
    }
}

function bulkUnpublish() {
    const selected = getSelectedPosts();
    if (selected.length === 0) {
        alert('Please select posts to unpublish.');
        return;
    }
    
    if (confirm(`Unpublish ${selected.length} selected posts?`)) {
        // Implementation would go here
        alert('Bulk unpublish functionality would be implemented here.');
    }
}

function bulkDelete() {
    const selected = getSelectedPosts();
    if (selected.length === 0) {
        alert('Please select posts to delete.');
        return;
    }
    
    if (confirm(`Delete ${selected.length} selected posts? This action cannot be undone.`)) {
        // Implementation would go here
        alert('Bulk delete functionality would be implemented here.');
    }
}
</script>
{% endblock %}
