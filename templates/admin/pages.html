{% extends "admin/base.html" %}

{% block title %}Pages - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">Pages</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin_new_page') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            New Page
        </a>
    </div>
</div>

<!-- Pages Table -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">All Pages ({{ pages|length }})</h5>
            </div>
            <div class="col-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Search pages..." id="search-pages">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    {% if pages %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Title</th>
                        <th>Status</th>
                        <th>Menu Order</th>
                        <th>Created</th>
                        <th>Updated</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for page in pages %}
                        <tr>
                            <td>
                                <div class="d-flex flex-column">
                                    <strong>{{ page.title }}</strong>
                                    <small class="text-muted">{{ page.content[:100] }}{% if page.content|length > 100 %}...{% endif %}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if page.published else 'warning' }}">
                                    {{ 'Published' if page.published else 'Draft' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ page.menu_order }}</span>
                            </td>
                            <td>
                                <small>{{ page.created_at.strftime('%b %d, %Y') }}</small>
                            </td>
                            <td>
                                <small>{{ page.updated_at.strftime('%b %d, %Y') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('view_page', slug=page.slug) }}" 
                                       class="btn btn-outline-info" target="_blank" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('admin_edit_page', slug=page.slug) }}" 
                                       class="btn btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-outline-danger" 
                                            onclick="deletePage('{{ page.slug }}')" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="card-body text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No pages yet</h4>
            <p class="text-muted">Create your first page to get started.</p>
            <a href="{{ url_for('admin_new_page') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create First Page
            </a>
        </div>
    {% endif %}
</div>

<!-- Bulk Actions -->
{% if pages %}
<div class="mt-3">
    <div class="card">
        <div class="card-body">
            <h6 class="card-title">Bulk Actions</h6>
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="select-all-pages">
                        <label class="form-check-label" for="select-all-pages">
                            Select all pages
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="bulkPublish()">
                            <i class="fas fa-check me-1"></i>
                            Publish Selected
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="bulkUnpublish()">
                            <i class="fas fa-eye-slash me-1"></i>
                            Unpublish Selected
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="bulkDelete()">
                            <i class="fas fa-trash me-1"></i>
                            Delete Selected
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// Search functionality
document.getElementById('search-pages').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const title = row.querySelector('td:first-child strong').textContent.toLowerCase();
        const content = row.querySelector('td:first-child small').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || content.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Delete page
function deletePage(slug) {
    if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
        fetch(`/api/pages/${slug}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                location.reload();
            } else {
                alert('Error deleting page: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Error deleting page: ' + error.message);
        });
    }
}

// Bulk actions
document.getElementById('select-all-pages').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function getSelectedPages() {
    const selected = [];
    document.querySelectorAll('tbody input[type="checkbox"]:checked').forEach(cb => {
        selected.push(cb.value);
    });
    return selected;
}

function bulkPublish() {
    const selected = getSelectedPages();
    if (selected.length === 0) {
        alert('Please select pages to publish.');
        return;
    }
    
    if (confirm(`Publish ${selected.length} selected pages?`)) {
        // Implementation would go here
        alert('Bulk publish functionality would be implemented here.');
    }
}

function bulkUnpublish() {
    const selected = getSelectedPages();
    if (selected.length === 0) {
        alert('Please select pages to unpublish.');
        return;
    }
    
    if (confirm(`Unpublish ${selected.length} selected pages?`)) {
        // Implementation would go here
        alert('Bulk unpublish functionality would be implemented here.');
    }
}

function bulkDelete() {
    const selected = getSelectedPages();
    if (selected.length === 0) {
        alert('Please select pages to delete.');
        return;
    }
    
    if (confirm(`Delete ${selected.length} selected pages? This action cannot be undone.`)) {
        // Implementation would go here
        alert('Bulk delete functionality would be implemented here.');
    }
}
</script>
{% endblock %}
