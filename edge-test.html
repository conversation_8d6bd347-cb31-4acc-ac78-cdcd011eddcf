<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edge Voice Assistant Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        #log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎙️ Edge Voice Assistant Diagnostic</h1>
        
        <div id="browser-info" class="status info">
            Detecting browser...
        </div>
        
        <div id="api-support" class="status info">
            Checking API support...
        </div>
        
        <div id="permission-status" class="status info">
            Checking permissions...
        </div>
        
        <div class="controls">
            <button id="test-permission" class="btn">Test Microphone Permission</button>
            <button id="test-recognition" class="btn">Test Speech Recognition</button>
            <button id="test-synthesis" class="btn">Test Speech Synthesis</button>
            <button id="clear-log" class="btn">Clear Log</button>
        </div>
        
        <div id="log"></div>
    </div>

    <script>
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            log.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog(args.join(' '), 'warning');
        };
        
        // Browser detection
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            const browserInfo = document.getElementById('browser-info');
            
            let browserName = 'Unknown';
            let isEdge = false;
            
            if (userAgent.includes('Edg/')) {
                browserName = 'Microsoft Edge (Chromium)';
                isEdge = true;
            } else if (userAgent.includes('Edge/')) {
                browserName = 'Microsoft Edge (Legacy)';
                isEdge = true;
            } else if (userAgent.includes('Chrome/')) {
                browserName = 'Google Chrome';
            } else if (userAgent.includes('Safari/')) {
                browserName = 'Safari';
            } else if (userAgent.includes('Firefox/')) {
                browserName = 'Firefox';
            }
            
            browserInfo.textContent = `Browser: ${browserName}`;
            browserInfo.className = isEdge ? 'status info' : 'status warning';
            
            console.log('Browser detected:', browserName);
            console.log('User Agent:', userAgent);
            console.log('Is Edge:', isEdge);
            
            return { browserName, isEdge, userAgent };
        }
        
        // Check API support
        function checkAPISupport() {
            const apiInfo = document.getElementById('api-support');
            const results = [];
            
            // Speech Recognition
            const speechRecognition = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
            results.push(`Speech Recognition: ${speechRecognition ? '✅' : '❌'}`);
            
            // Speech Synthesis
            const speechSynthesis = 'speechSynthesis' in window;
            results.push(`Speech Synthesis: ${speechSynthesis ? '✅' : '❌'}`);
            
            // Media Devices
            const mediaDevices = 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
            results.push(`Media Devices: ${mediaDevices ? '✅' : '❌'}`);
            
            // Permissions API
            const permissions = 'permissions' in navigator;
            results.push(`Permissions API: ${permissions ? '✅' : '❌'}`);
            
            apiInfo.innerHTML = results.join('<br>');
            apiInfo.className = speechRecognition && speechSynthesis && mediaDevices ? 'status success' : 'status error';
            
            console.log('API Support Check:', results);
        }
        
        // Test microphone permission
        async function testMicrophonePermission() {
            console.log('Testing microphone permission...');
            const permissionStatus = document.getElementById('permission-status');
            
            try {
                // Check current permission state
                if (navigator.permissions) {
                    try {
                        const permission = await navigator.permissions.query({ name: 'microphone' });
                        console.log('Permission state:', permission.state);
                        
                        permission.addEventListener('change', () => {
                            console.log('Permission changed to:', permission.state);
                        });
                    } catch (e) {
                        console.log('Permission query failed:', e.message);
                    }
                }
                
                // Request microphone access
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                
                console.log('Microphone access granted!');
                console.log('Audio tracks:', stream.getAudioTracks().length);
                
                // Stop the stream
                stream.getTracks().forEach(track => {
                    console.log('Stopping track:', track.label);
                    track.stop();
                });
                
                permissionStatus.textContent = '✅ Microphone permission granted';
                permissionStatus.className = 'status success';
                
            } catch (error) {
                console.error('Microphone permission error:', error);
                console.error('Error details:', {
                    name: error.name,
                    message: error.message,
                    code: error.code
                });
                
                permissionStatus.textContent = `❌ Microphone error: ${error.name}`;
                permissionStatus.className = 'status error';
            }
        }
        
        // Test speech recognition
        function testSpeechRecognition() {
            console.log('Testing speech recognition...');
            
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                console.error('Speech recognition not supported');
                return;
            }
            
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            console.log('Using constructor:', SpeechRecognition.name);
            
            try {
                const recognition = new SpeechRecognition();
                console.log('Speech recognition object created');
                
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'en-US';
                
                recognition.onstart = () => {
                    console.log('✅ Speech recognition started - say "test"');
                };
                
                recognition.onresult = (event) => {
                    let transcript = '';
                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        transcript += event.results[i][0].transcript;
                    }
                    console.log('Heard:', transcript);
                };
                
                recognition.onerror = (event) => {
                    console.error('Speech recognition error:', event.error);
                    console.error('Error details:', event);
                };
                
                recognition.onend = () => {
                    console.log('Speech recognition ended');
                };
                
                recognition.start();
                
                // Stop after 5 seconds
                setTimeout(() => {
                    recognition.stop();
                    console.log('Stopping speech recognition test');
                }, 5000);
                
            } catch (error) {
                console.error('Failed to create speech recognition:', error);
            }
        }
        
        // Test speech synthesis
        function testSpeechSynthesis() {
            console.log('Testing speech synthesis...');
            
            if (!('speechSynthesis' in window)) {
                console.error('Speech synthesis not supported');
                return;
            }
            
            const utterance = new SpeechSynthesisUtterance('Hello from Edge! This is a test of speech synthesis.');
            
            utterance.onstart = () => console.log('✅ Speech synthesis started');
            utterance.onend = () => console.log('✅ Speech synthesis ended');
            utterance.onerror = (e) => console.error('Speech synthesis error:', e);
            
            // Try to find a good voice
            const voices = speechSynthesis.getVoices();
            console.log('Available voices:', voices.length);
            voices.forEach((voice, i) => {
                console.log(`Voice ${i}: ${voice.name} (${voice.lang})`);
            });
            
            speechSynthesis.speak(utterance);
        }
        
        // Event listeners
        document.getElementById('test-permission').addEventListener('click', testMicrophonePermission);
        document.getElementById('test-recognition').addEventListener('click', testSpeechRecognition);
        document.getElementById('test-synthesis').addEventListener('click', testSpeechSynthesis);
        document.getElementById('clear-log').addEventListener('click', () => {
            log.textContent = '';
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Edge Voice Assistant Diagnostic loaded');
            detectBrowser();
            checkAPISupport();
            
            // Load voices
            if (speechSynthesis.getVoices().length === 0) {
                speechSynthesis.addEventListener('voiceschanged', () => {
                    console.log('Voices loaded:', speechSynthesis.getVoices().length);
                });
            }
        });
    </script>
</body>
</html>
