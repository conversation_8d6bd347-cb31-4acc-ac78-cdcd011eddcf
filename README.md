# Markdown-Powered Conversational CMS

A modern, efficient Content Management System that combines the simplicity of Markdown with the power of AI-driven conversations. Perfect for bloggers, documentation sites, and anyone who wants an engaging, interactive website.

## 🌟 Features

### Content Management
- **Markdown-First**: Write content in clean, simple Markdown format
- **File-Based Storage**: No complex database setup required
- **Live Preview**: See your content as you write
- **Media Management**: Easy file uploads and organization
- **SEO Optimized**: Clean URLs and proper meta tags

### AI-Powered Conversations
- **Local LLM Integration**: Works with Ollama, LM Studio, and other local AI services
- **Talk Show Host Persona**: Friendly AI that welcomes visitors and helps them explore content
- **Context-Aware**: AI understands your content and can discuss it intelligently
- **Privacy-Focused**: All AI processing happens locally on your machine

### Technical Excellence
- **Fast & Lightweight**: Minimal dependencies, maximum performance
- **Responsive Design**: Looks great on all devices
- **RESTful API**: Easy integration with other tools
- **Extensible**: Add features as you need them

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- A local LLM service (recommended: [Ollama](https://ollama.ai/))

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd MarkdownCMS
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your preferred settings
   ```

4. **Set up local LLM (recommended)**
   ```bash
   # Install Ollama (macOS/Linux)
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Pull a model
   ollama pull llama2
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Open your browser**
   Navigate to `http://localhost:5000`

## 📖 Usage

### Creating Content

1. **Access Admin Panel**: Go to `/admin`
2. **Create Posts**: Click "New Post" and write in Markdown
3. **Create Pages**: Click "New Page" for static content
4. **Upload Media**: Use the file upload feature for images and documents

### AI Chat Features

The AI chat interface appears in the sidebar and can:
- Welcome new visitors
- Help users discover relevant content
- Answer questions about your posts and pages
- Engage in conversations about your topics

### Markdown Support

Full GitHub Flavored Markdown support including:
- Headers, lists, and formatting
- Code blocks with syntax highlighting
- Tables and blockquotes
- Images and links
- Custom HTML when needed

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# LLM Settings
LLM_BASE_URL=http://localhost:11434  # Ollama default
LLM_MODEL=llama2                     # Model name
LLM_TEMPERATURE=0.7                  # Response creativity

# Flask Settings
SECRET_KEY=your-secret-key
FLASK_ENV=development

# Content Settings
CONTENT_DIR=content
UPLOAD_DIR=static/uploads
```

### LLM Integration

This CMS works with various local LLM services:

- **Ollama** (recommended): Easy setup, good performance
- **LM Studio**: User-friendly GUI
- **Text Generation WebUI**: Advanced features
- **Custom APIs**: Implement your own integration

## 📁 Project Structure

```
MarkdownCMS/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── .env.example          # Configuration template
├── content/              # Markdown content files
│   ├── posts/           # Blog posts
│   └── pages/           # Static pages
├── services/            # Business logic
│   ├── content_manager.py
│   ├── ai_service.py
│   └── conversation_manager.py
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── post.html
│   ├── page.html
│   └── admin/
├── static/             # CSS, JS, and uploaded files
│   ├── css/
│   ├── js/
│   └── uploads/
└── utils/              # Helper functions
```

## 🎨 Customization

### Templates
- Modify HTML templates in the `templates/` directory
- Use Jinja2 templating syntax
- Extend the base template for consistency

### Styling
- Edit `static/css/style.css` for custom styles
- Bootstrap 5 is included by default
- Responsive design built-in

### AI Personality
- Modify the system prompt in `services/ai_service.py`
- Customize conversation flows
- Add domain-specific knowledge

## 🔌 API Endpoints

### Content API
- `GET /api/posts` - List all posts
- `POST /api/posts` - Create new post
- `GET /api/posts/<slug>` - Get specific post
- `PUT /api/posts/<slug>` - Update post
- `DELETE /api/posts/<slug>` - Delete post

### Chat API
- `POST /api/chat` - Send message to AI

### Upload API
- `POST /api/upload` - Upload files

## 🛠️ Development

### Running in Development Mode
```bash
export FLASK_ENV=development
export FLASK_DEBUG=True
python app.py
```

### Adding Features
1. Create new services in `services/`
2. Add routes to `app.py`
3. Create templates in `templates/`
4. Add styles to `static/css/`

### Testing
```bash
# Run basic tests
python -m pytest tests/

# Test AI integration
curl -X POST http://localhost:5000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello!"}'
```

## 📦 Deployment

### Local Deployment
The CMS runs great on local machines for personal use or small teams.

### Server Deployment
1. Use a WSGI server like Gunicorn
2. Set up a reverse proxy (Nginx)
3. Configure environment variables
4. Set up your local LLM service

### Docker Deployment
```bash
# Build image
docker build -t markdown-cms .

# Run container
docker run -p 5000:5000 markdown-cms
```

## 🤝 Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check the `/admin` panel for built-in help
- **AI Chat**: Ask the AI host for guidance
- **Issues**: Report bugs on GitHub
- **Discussions**: Join community discussions

## 🙏 Acknowledgments

- Built with [Flask](https://flask.palletsprojects.com/)
- Styled with [Bootstrap](https://getbootstrap.com/)
- Markdown processing by [Python-Markdown](https://python-markdown.github.io/)
- AI integration designed for [Ollama](https://ollama.ai/)

---

**Ready to start creating?** Run the application and visit the admin panel to begin building your content library!
