#!/usr/bin/env python3
"""
Markdown-Powered Conversational CMS
Main Flask Application
"""

import os
import json
import threading
import time
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_cors import CORS
from werkzeug.utils import secure_filename

from services.content_manager import ContentManager
from services.ai_service import AIService
from services.conversation_manager import ConversationManager
from services.auto_content_generator import AutoContentGenerator
from utils.helpers import allowed_file, get_file_extension
import markdown

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-this')
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

CORS(app)

# Initialize services
content_manager = ContentManager()
ai_service = AIService(content_manager)
conversation_manager = ConversationManager()
auto_generator = AutoContentGenerator(content_manager, ai_service, conversation_manager)

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Add markdown filter for templates
@app.template_filter('markdown')
def markdown_filter(text):
    """Convert markdown to HTML"""
    from markupsafe import Markup

    html = markdown.markdown(text, extensions=[
        'markdown.extensions.codehilite',
        'markdown.extensions.fenced_code',
        'markdown.extensions.tables',
        'markdown.extensions.toc',
        'markdown.extensions.nl2br'
    ])

    return Markup(html)

@app.route('/')
def index():
    """Homepage with latest content and chat interface"""
    posts = content_manager.get_all_posts(limit=5)
    pages = content_manager.get_all_pages()
    return render_template('index.html', posts=posts, pages=pages)

@app.route('/post/<slug>')
def view_post(slug):
    """View individual blog post"""
    post = content_manager.get_post_by_slug(slug)
    if not post:
        return render_template('404.html'), 404
    return render_template('post.html', post=post)

@app.route('/page/<slug>')
def view_page(slug):
    """View individual page"""
    page = content_manager.get_page_by_slug(slug)
    if not page:
        return render_template('404.html'), 404
    return render_template('page.html', page=page)

@app.route('/admin')
def admin_dashboard():
    """Admin dashboard"""
    posts = content_manager.get_all_posts()
    pages = content_manager.get_all_pages()
    return render_template('admin/dashboard.html', posts=posts, pages=pages)

@app.route('/admin/posts')
def admin_posts():
    """Admin posts management"""
    posts = content_manager.get_all_posts()
    return render_template('admin/posts.html', posts=posts)

@app.route('/admin/posts/new')
def admin_new_post():
    """Create new post form"""
    return render_template('admin/edit_post.html', post=None, action='create')

@app.route('/admin/posts/edit/<slug>')
def admin_edit_post(slug):
    """Edit existing post form"""
    post = content_manager.get_post_by_slug(slug)
    if not post:
        flash('Post not found', 'error')
        return redirect(url_for('admin_posts'))
    return render_template('admin/edit_post.html', post=post, action='edit')

@app.route('/admin/pages')
def admin_pages():
    """Admin pages management"""
    pages = content_manager.get_all_pages()
    return render_template('admin/pages.html', pages=pages)

@app.route('/admin/pages/new')
def admin_new_page():
    """Create new page form"""
    return render_template('admin/edit_page.html', page=None, action='create')

@app.route('/admin/pages/edit/<slug>')
def admin_edit_page(slug):
    """Edit existing page form"""
    page = content_manager.get_page_by_slug(slug)
    if not page:
        flash('Page not found', 'error')
        return redirect(url_for('admin_pages'))
    return render_template('admin/edit_page.html', page=page, action='edit')

@app.route('/admin/ai-settings')
def admin_ai_settings():
    """AI configuration and model management"""
    return render_template('admin/ai_settings.html')

@app.route('/admin/web-search-settings')
def admin_web_search_settings():
    """Web search configuration and statistics"""
    # Get web search configuration
    config = {
        'enable_web_search': os.environ.get('ENABLE_WEB_SEARCH', 'true').lower() == 'true',
        'search_confidence_threshold': float(os.environ.get('SEARCH_CONFIDENCE_THRESHOLD', '0.6')),
        'search_max_results': int(os.environ.get('SEARCH_MAX_RESULTS', '5')),
        'search_timeout': int(os.environ.get('SEARCH_TIMEOUT_SECONDS', '10')),
        'search_log_dir': os.environ.get('SEARCH_LOG_DIR', 'data/search_logs'),
        'bing_api_key': os.environ.get('BING_SEARCH_API_KEY', ''),
        'google_api_key': os.environ.get('GOOGLE_SEARCH_API_KEY', ''),
        'google_cx': os.environ.get('GOOGLE_SEARCH_CX', ''),
        'serp_api_key': os.environ.get('SERP_API_KEY', ''),
        'use_duckduckgo_fallback': os.environ.get('USE_DUCKDUCKGO_FALLBACK', 'true').lower() == 'true',
        'enable_search_logging': os.environ.get('ENABLE_SEARCH_LOGGING', 'true').lower() == 'true'
    }
    
    # Get search statistics
    stats = {
        'total_searches': 0,
        'successful_searches': 0,
        'avg_results': 0,
        'avg_response_time': 0
    }
    
    # Get recent searches
    recent_searches = []
    
    try:
        search_log_dir = os.environ.get('SEARCH_LOG_DIR', 'data/search_logs')
        if os.path.exists(search_log_dir):
            log_files = sorted([f for f in os.listdir(search_log_dir) if f.startswith('search_')], reverse=True)
            
            # Calculate stats
            total_results = 0
            total_response_time = 0
            successful_searches = 0
            
            # Process recent logs
            for i, log_file in enumerate(log_files[:20]):  # Get stats from last 20 logs
                try:
                    with open(os.path.join(search_log_dir, log_file), 'r') as f:
                        log_data = json.load(f)
                        
                        # Update stats
                        result_count = log_data.get('result_count', 0)
                        if result_count > 0:
                            successful_searches += 1
                            total_results += result_count
                            total_response_time += log_data.get('response_time', 0)
                        
                        # Add to recent searches (first 10 only)
                        if i < 10:
                            recent_searches.append(log_data)
                except:
                    continue
            
            # Calculate averages
            stats['total_searches'] = len(log_files)
            stats['successful_searches'] = successful_searches
            
            if successful_searches > 0:
                stats['avg_results'] = round(total_results / successful_searches, 1)
                stats['avg_response_time'] = round(total_response_time / successful_searches, 2)
    except Exception as e:
        print(f"Error getting search statistics: {e}")
    
    return render_template('admin/web_search_settings.html', 
                          config=config, 
                          stats=stats, 
                          recent_searches=recent_searches)

# API Routes
@app.route('/api/chat', methods=['POST'])
def api_chat():
    """Handle chat messages with AI"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        conversation_id = data.get('conversation_id')
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Get or create conversation
        if not conversation_id:
            conversation_id = conversation_manager.create_conversation()
        
        # Add user message to conversation
        conversation_manager.add_message(conversation_id, 'user', message)
        
        # Get AI response
        ai_response = ai_service.get_response(message, conversation_id)
        
        # Add AI response to conversation
        conversation_manager.add_message(conversation_id, 'assistant', ai_response)
        
        return jsonify({
            'response': ai_response,
            'conversation_id': conversation_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/posts', methods=['GET', 'POST'])
def api_posts():
    """Get all posts or create new post"""
    if request.method == 'GET':
        posts = content_manager.get_all_posts()
        return jsonify([post.to_dict() for post in posts])
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            post = content_manager.create_post(
                title=data['title'],
                content=data['content'],
                slug=data.get('slug'),
                tags=data.get('tags', []),
                published=data.get('published', False)
            )
            return jsonify(post.to_dict()), 201
        except Exception as e:
            return jsonify({'error': str(e)}), 400

@app.route('/api/posts/<slug>', methods=['GET', 'PUT', 'DELETE'])
def api_post(slug):
    """Get, update, or delete specific post"""
    if request.method == 'GET':
        post = content_manager.get_post_by_slug(slug)
        if not post:
            return jsonify({'error': 'Post not found'}), 404
        return jsonify(post.to_dict())
    
    elif request.method == 'PUT':
        try:
            data = request.get_json()
            post = content_manager.update_post(
                slug=slug,
                title=data.get('title'),
                content=data.get('content'),
                tags=data.get('tags'),
                published=data.get('published')
            )
            if not post:
                return jsonify({'error': 'Post not found'}), 404
            return jsonify(post.to_dict())
        except Exception as e:
            return jsonify({'error': str(e)}), 400
    
    elif request.method == 'DELETE':
        success = content_manager.delete_post(slug)
        if not success:
            return jsonify({'error': 'Post not found'}), 404
        return jsonify({'message': 'Post deleted successfully'})

@app.route('/api/pages', methods=['GET', 'POST'])
def api_pages():
    """Get all pages or create new page"""
    if request.method == 'GET':
        pages = content_manager.get_all_pages()
        return jsonify([page.to_dict() for page in pages])
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            page = content_manager.create_page(
                title=data['title'],
                content=data['content'],
                slug=data.get('slug'),
                published=data.get('published', False)
            )
            return jsonify(page.to_dict()), 201
        except Exception as e:
            return jsonify({'error': str(e)}), 400

@app.route('/api/models', methods=['GET'])
def api_models():
    """Get available AI models"""
    try:
        models = ai_service.get_available_models()
        current_model = ai_service.model_name
        is_available = ai_service.is_available()

        return jsonify({
            'models': models,
            'current_model': current_model,
            'is_available': is_available
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/models/switch', methods=['POST'])
def api_switch_model():
    """Switch to a different AI model"""
    try:
        data = request.get_json()
        model_name = data.get('model_name')

        if not model_name:
            return jsonify({'error': 'Model name is required'}), 400

        # Get available models to validate
        available_models = ai_service.get_available_models()
        if model_name not in available_models:
            return jsonify({'error': 'Model not available'}), 400

        # Switch model
        ai_service.set_model(model_name)

        return jsonify({
            'message': f'Switched to model: {model_name}',
            'current_model': model_name
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auto-generation/stats', methods=['GET'])
def api_auto_generation_stats():
    """Get auto-generation statistics"""
    try:
        stats = auto_generator.get_generation_stats()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auto-generation/preview', methods=['GET'])
def api_auto_generation_preview():
    """Preview next auto-generated post"""
    try:
        preview = auto_generator.preview_next_post()
        return jsonify(preview)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auto-generation/generate', methods=['POST'])
def api_auto_generation_generate():
    """Manually trigger post generation"""
    try:
        post_slug = auto_generator.generate_daily_post()
        if post_slug:
            return jsonify({
                'message': 'Post generated successfully',
                'post_slug': post_slug
            })
        else:
            return jsonify({
                'message': 'No post generated - insufficient conversation data or already generated today'
            })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auto-generation/config', methods=['GET', 'POST'])
def api_auto_generation_config():
    """Get or update auto-generation configuration"""
    if request.method == 'GET':
        try:
            return jsonify(auto_generator.config)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            auto_generator.update_config(data)
            return jsonify({
                'message': 'Configuration updated successfully',
                'config': auto_generator.config
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500
            
@app.route('/api/web-search/config', methods=['GET', 'POST'])
def api_web_search_config():
    """Get or update web search configuration"""
    if request.method == 'GET':
        try:
            # Get current configuration
            config = {
                'enable_web_search': os.environ.get('ENABLE_WEB_SEARCH', 'true').lower() == 'true',
                'search_confidence_threshold': float(os.environ.get('SEARCH_CONFIDENCE_THRESHOLD', '0.6')),
                'search_max_results': int(os.environ.get('SEARCH_MAX_RESULTS', '5')),
                'search_timeout': int(os.environ.get('SEARCH_TIMEOUT_SECONDS', '10')),
                'search_log_dir': os.environ.get('SEARCH_LOG_DIR', 'data/search_logs'),
                'bing_api_key': os.environ.get('BING_SEARCH_API_KEY', ''),
                'google_api_key': os.environ.get('GOOGLE_SEARCH_API_KEY', ''),
                'google_cx': os.environ.get('GOOGLE_SEARCH_CX', ''),
                'serp_api_key': os.environ.get('SERP_API_KEY', ''),
                'use_duckduckgo_fallback': os.environ.get('USE_DUCKDUCKGO_FALLBACK', 'true').lower() == 'true',
                'enable_search_logging': os.environ.get('ENABLE_SEARCH_LOGGING', 'true').lower() == 'true'
            }
            return jsonify(config)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # Update environment variables
            # Note: This only updates the current process environment
            # For persistence, you would need to update the .env file
            os.environ['ENABLE_WEB_SEARCH'] = str(data.get('enable_web_search', True)).lower()
            os.environ['SEARCH_CONFIDENCE_THRESHOLD'] = str(data.get('search_confidence_threshold', 0.6))
            os.environ['SEARCH_MAX_RESULTS'] = str(data.get('search_max_results', 5))
            os.environ['SEARCH_TIMEOUT_SECONDS'] = str(data.get('search_timeout', 10))
            os.environ['SEARCH_LOG_DIR'] = data.get('search_log_dir', 'data/search_logs')
            
            # API keys
            if 'bing_api_key' in data:
                os.environ['BING_SEARCH_API_KEY'] = data['bing_api_key']
            if 'google_api_key' in data:
                os.environ['GOOGLE_SEARCH_API_KEY'] = data['google_api_key']
            if 'google_cx' in data:
                os.environ['GOOGLE_SEARCH_CX'] = data['google_cx']
            if 'serp_api_key' in data:
                os.environ['SERP_API_KEY'] = data['serp_api_key']
                
            os.environ['USE_DUCKDUCKGO_FALLBACK'] = str(data.get('use_duckduckgo_fallback', True)).lower()
            os.environ['ENABLE_SEARCH_LOGGING'] = str(data.get('enable_search_logging', True)).lower()
            
            # Update AI service configuration
            ai_service.enable_web_search = data.get('enable_web_search', True)
            ai_service.search_threshold = float(data.get('search_confidence_threshold', 0.6))
            ai_service.search_log_dir = data.get('search_log_dir', 'data/search_logs')
            
            # Update web search service configuration
            ai_service.web_search.max_results = int(data.get('search_max_results', 5))
            ai_service.web_search.timeout = int(data.get('search_timeout', 10))
            
            # Update API keys
            if 'bing_api_key' in data:
                ai_service.web_search.bing_api_key = data['bing_api_key']
            if 'google_api_key' in data:
                ai_service.web_search.google_api_key = data['google_api_key']
            if 'google_cx' in data:
                ai_service.web_search.google_cx = data['google_cx']
            if 'serp_api_key' in data:
                ai_service.web_search.serp_api_key = data['serp_api_key']
            
            # Create log directory if it doesn't exist
            os.makedirs(data.get('search_log_dir', 'data/search_logs'), exist_ok=True)
            
            return jsonify({
                'success': True,
                'message': 'Web search configuration updated successfully'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
            
@app.route('/api/web-search/test', methods=['POST'])
def api_test_web_search():
    """Test web search functionality"""
    try:
        data = request.get_json()
        query = data.get('query')
        
        if not query:
            return jsonify({'error': 'Query is required'}), 400
            
        # Perform search
        search_results = ai_service.web_search.search(query)
        
        return jsonify(search_results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def api_upload():
    """Handle file uploads"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{name}_{timestamp}{ext}"
        
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        return jsonify({
            'filename': filename,
            'url': f"/static/uploads/{filename}",
            'message': 'File uploaded successfully'
        })
    
    return jsonify({'error': 'File type not allowed'}), 400

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# Background scheduler for auto-generation
def daily_post_scheduler():
    """Background thread to check for daily post generation"""
    while True:
        try:
            now = datetime.now()
            # Check if it's time to generate (9 AM daily)
            if now.hour == 9 and now.minute == 0:
                print("Checking for daily post generation...")
                post_slug = auto_generator.generate_daily_post()
                if post_slug:
                    print(f"Auto-generated daily post: {post_slug}")
                else:
                    print("No post generated today")

                # Sleep for 61 seconds to avoid multiple triggers in the same minute
                time.sleep(61)
            else:
                # Check every minute
                time.sleep(60)

        except Exception as e:
            print(f"Error in daily post scheduler: {e}")
            time.sleep(300)  # Wait 5 minutes on error

if __name__ == '__main__':
    # Create content directories
    os.makedirs('content/posts', exist_ok=True)
    os.makedirs('content/pages', exist_ok=True)
    os.makedirs('static/uploads', exist_ok=True)

    # Start background scheduler for auto-generation
    scheduler_thread = threading.Thread(target=daily_post_scheduler, daemon=True)
    scheduler_thread.start()
    print("Started daily post generation scheduler")

    app.run(debug=True, host='0.0.0.0', port=5000)
