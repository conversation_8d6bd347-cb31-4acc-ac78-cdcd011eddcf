#!/usr/bin/env python3
"""
Markdown-Powered Conversational CMS
Main Flask Application
"""

import os
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_cors import CORS
from werkzeug.utils import secure_filename

from services.content_manager import ContentManager
from services.ai_service import AIService
from services.conversation_manager import ConversationManager
from utils.helpers import allowed_file, get_file_extension
import markdown

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-this')
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

CORS(app)

# Initialize services
content_manager = ContentManager()
ai_service = AIService()
conversation_manager = ConversationManager()

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Add markdown filter for templates
@app.template_filter('markdown')
def markdown_filter(text):
    """Convert markdown to HTML"""
    return markdown.markdown(text, extensions=[
        'codehilite',
        'fenced_code',
        'tables',
        'toc'
    ])

@app.route('/')
def index():
    """Homepage with latest content and chat interface"""
    posts = content_manager.get_all_posts(limit=5)
    pages = content_manager.get_all_pages()
    return render_template('index.html', posts=posts, pages=pages)

@app.route('/post/<slug>')
def view_post(slug):
    """View individual blog post"""
    post = content_manager.get_post_by_slug(slug)
    if not post:
        return render_template('404.html'), 404
    return render_template('post.html', post=post)

@app.route('/page/<slug>')
def view_page(slug):
    """View individual page"""
    page = content_manager.get_page_by_slug(slug)
    if not page:
        return render_template('404.html'), 404
    return render_template('page.html', page=page)

@app.route('/admin')
def admin_dashboard():
    """Admin dashboard"""
    posts = content_manager.get_all_posts()
    pages = content_manager.get_all_pages()
    return render_template('admin/dashboard.html', posts=posts, pages=pages)

@app.route('/admin/posts')
def admin_posts():
    """Admin posts management"""
    posts = content_manager.get_all_posts()
    return render_template('admin/posts.html', posts=posts)

@app.route('/admin/posts/new')
def admin_new_post():
    """Create new post form"""
    return render_template('admin/edit_post.html', post=None, action='create')

@app.route('/admin/posts/edit/<slug>')
def admin_edit_post(slug):
    """Edit existing post form"""
    post = content_manager.get_post_by_slug(slug)
    if not post:
        flash('Post not found', 'error')
        return redirect(url_for('admin_posts'))
    return render_template('admin/edit_post.html', post=post, action='edit')

@app.route('/admin/pages')
def admin_pages():
    """Admin pages management"""
    pages = content_manager.get_all_pages()
    return render_template('admin/pages.html', pages=pages)

@app.route('/admin/pages/new')
def admin_new_page():
    """Create new page form"""
    return render_template('admin/edit_page.html', page=None, action='create')

@app.route('/admin/pages/edit/<slug>')
def admin_edit_page(slug):
    """Edit existing page form"""
    page = content_manager.get_page_by_slug(slug)
    if not page:
        flash('Page not found', 'error')
        return redirect(url_for('admin_pages'))
    return render_template('admin/edit_page.html', page=page, action='edit')

# API Routes
@app.route('/api/chat', methods=['POST'])
def api_chat():
    """Handle chat messages with AI"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        conversation_id = data.get('conversation_id')
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Get or create conversation
        if not conversation_id:
            conversation_id = conversation_manager.create_conversation()
        
        # Add user message to conversation
        conversation_manager.add_message(conversation_id, 'user', message)
        
        # Get AI response
        ai_response = ai_service.get_response(message, conversation_id)
        
        # Add AI response to conversation
        conversation_manager.add_message(conversation_id, 'assistant', ai_response)
        
        return jsonify({
            'response': ai_response,
            'conversation_id': conversation_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/posts', methods=['GET', 'POST'])
def api_posts():
    """Get all posts or create new post"""
    if request.method == 'GET':
        posts = content_manager.get_all_posts()
        return jsonify([post.to_dict() for post in posts])
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            post = content_manager.create_post(
                title=data['title'],
                content=data['content'],
                slug=data.get('slug'),
                tags=data.get('tags', []),
                published=data.get('published', False)
            )
            return jsonify(post.to_dict()), 201
        except Exception as e:
            return jsonify({'error': str(e)}), 400

@app.route('/api/posts/<slug>', methods=['GET', 'PUT', 'DELETE'])
def api_post(slug):
    """Get, update, or delete specific post"""
    if request.method == 'GET':
        post = content_manager.get_post_by_slug(slug)
        if not post:
            return jsonify({'error': 'Post not found'}), 404
        return jsonify(post.to_dict())
    
    elif request.method == 'PUT':
        try:
            data = request.get_json()
            post = content_manager.update_post(
                slug=slug,
                title=data.get('title'),
                content=data.get('content'),
                tags=data.get('tags'),
                published=data.get('published')
            )
            if not post:
                return jsonify({'error': 'Post not found'}), 404
            return jsonify(post.to_dict())
        except Exception as e:
            return jsonify({'error': str(e)}), 400
    
    elif request.method == 'DELETE':
        success = content_manager.delete_post(slug)
        if not success:
            return jsonify({'error': 'Post not found'}), 404
        return jsonify({'message': 'Post deleted successfully'})

@app.route('/api/pages', methods=['GET', 'POST'])
def api_pages():
    """Get all pages or create new page"""
    if request.method == 'GET':
        pages = content_manager.get_all_pages()
        return jsonify([page.to_dict() for page in pages])
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            page = content_manager.create_page(
                title=data['title'],
                content=data['content'],
                slug=data.get('slug'),
                published=data.get('published', False)
            )
            return jsonify(page.to_dict()), 201
        except Exception as e:
            return jsonify({'error': str(e)}), 400

@app.route('/api/upload', methods=['POST'])
def api_upload():
    """Handle file uploads"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{name}_{timestamp}{ext}"
        
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        return jsonify({
            'filename': filename,
            'url': f"/static/uploads/{filename}",
            'message': 'File uploaded successfully'
        })
    
    return jsonify({'error': 'File type not allowed'}), 400

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    # Create content directories
    os.makedirs('content/posts', exist_ok=True)
    os.makedirs('content/pages', exist_ok=True)
    os.makedirs('static/uploads', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
